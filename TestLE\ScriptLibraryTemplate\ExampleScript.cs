using System.Collections;
using TestLE.Scripting;
using MelonLoader;
using UnityEngine;

namespace MyScriptLibrary;

/// <summary>
/// Example script that demonstrates the basic structure of a script routine.
/// This script will be automatically discovered and loaded by the script manager.
/// </summary>
public class ExampleScript : ScriptBase, IUpdatableScript
{
    public override string Name => "Example Script";

    private int _updateCounter = 0;

    public override bool CanExecute()
    {
        // Only execute if we have a player
        return Player != null;
    }

    public override IEnumerator Execute()
    {
        Log("Starting example script execution");

        // Example of a simple coroutine that runs for 5 seconds
        var startTime = DateTime.Now;
        while ((DateTime.Now - startTime).TotalSeconds < 5)
        {
            Log($"Script running... {(DateTime.Now - startTime).TotalSeconds:F1}s");
            yield return new WaitForSeconds(1f);
        }

        Log("Example script execution completed");
    }

    public void OnUpdate()
    {
        // This method is called every frame when the script is active
        _updateCounter++;
        
        // Log every 60 frames (roughly once per second at 60 FPS)
        if (_updateCounter % 60 == 0)
        {
            Log($"Update called {_updateCounter} times");
        }
    }

    public override void OnLoad()
    {
        base.OnLoad();
        Log("Example script loaded and ready");
    }

    public override void OnUnload()
    {
        Log("Example script unloading");
        base.OnUnload();
    }
}

/// <summary>
/// Another example script that demonstrates GUI functionality.
/// </summary>
public class ExampleGuiScript : ScriptBase, IGuiScript
{
    public override string Name => "Example GUI Script";

    private bool _showWindow = false;
    private string _inputText = "Hello World!";

    public override bool CanExecute()
    {
        return true; // This script can always execute
    }

    public override IEnumerator Execute()
    {
        Log("GUI Script started - press F1 to toggle window");
        _showWindow = true;
        
        // This script runs indefinitely
        while (true)
        {
            yield return new WaitForSeconds(0.1f);
        }
    }

    public void OnGUI()
    {
        // Check for F1 key to toggle window
        if (Event.current.type == EventType.KeyDown &&
            Event.current.keyCode == KeyCode.F1)
        {
            _showWindow = !_showWindow;
        }

        if (_showWindow)
        {
            // Create a simple window
            GUILayout.Window(12345, new Rect(100, 100, 300, 200), (GUI.WindowFunction)DrawWindow, "Example Script Window");
        }
    }

    private void DrawWindow(int windowId)
    {
        GUILayout.Label("This is an example script window!");
        GUILayout.Label($"Current Scene: {CurrentScene}");
        GUILayout.Label($"Player Position: {Player?.transform?.position}");

        GUILayout.Space(10);

        GUILayout.Label("Input Text:");
        _inputText = GUILayout.TextField(_inputText);

        if (GUILayout.Button("Log Message"))
        {
            Log($"Button clicked! Message: {_inputText}");
        }

        if (GUILayout.Button("Close Window"))
        {
            _showWindow = false;
        }

        // Make window draggable
        GUI.DragWindow();
    }
}
