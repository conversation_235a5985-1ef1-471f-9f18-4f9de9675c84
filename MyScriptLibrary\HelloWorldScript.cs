using System.Collections;
using TestLE.Scripting;
using MelonLoader;
using UnityEngine;

namespace MyScriptLibrary;

/// <summary>
/// A simple "Hello World" script that demonstrates basic functionality.
/// </summary>
public class HelloWorldScript : ScriptBase
{
    public override string Name => "Hello World Script";

    public override bool CanExecute()
    {
        // This script can always run
        return true;
    }

    public override IEnumerator Execute()
    {
        Log("🌟 Hello World! Script is starting... (Hot Reload Test v1.0)");

        // Wait for 1 second
        yield return new WaitForSeconds(1f);

        Log("🚀 Script is running smoothly...");

        // Wait for another 2 seconds
        yield return new WaitForSeconds(2f);

        Log("✅ Hello World script completed successfully!");
        Log("💡 Try modifying this script and rebuilding to test hot reloading!");
    }

    public override void OnLoad()
    {
        base.OnLoad();
        Log("Hello World script has been loaded and is ready to run!");
    }

    public override void OnUnload()
    {
        Log("Hello World script is being unloaded. Goodbye!");
        base.OnUnload();
    }
}
