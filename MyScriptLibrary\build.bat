@echo off
echo 🔨 Building MyScriptLibrary for hot reloading...
echo.

dotnet build MyScriptLibrary.csproj

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Build successful!
    echo 🔥 Hot reload ready: The DLL has been copied to the TestLE ScriptLibraries folder.
    echo 🎮 If the game is running, the library should automatically reload in a few seconds.
    echo 📋 Available scripts:
    echo    - MyScriptLibrary.HelloWorldScript
    echo    - MyScriptLibrary.PlayerInfoScript
    echo    - MyScriptLibrary.SimpleGUIScript (press F2 for GUI)
    echo.
    echo 💡 Tip: Make changes to your scripts and run this build script again for instant hot reloading!
) else (
    echo.
    echo ❌ Build failed! Check the error messages above.
    echo 🔧 Make sure the TestLE mod is properly installed and the game paths are correct.
)
echo.
pause
