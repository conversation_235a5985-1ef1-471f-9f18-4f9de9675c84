using Melon<PERSON>oader;

namespace TestLE.Scripting;

/// <summary>
/// Simple integration for C# scripting system.
/// Provides easy-to-use methods for initializing and managing C# scripts.
/// </summary>
public static class CSharpIntegration
{
    /// <summary>
    /// Initialize the C# scripting system.
    /// Call this from your mod's OnApplicationStart.
    /// </summary>
    public static void Initialize()
    {
        CSharpScriptManager.Instance.Initialize();
    }

    /// <summary>
    /// Shutdown the C# scripting system.
    /// Call this from your mod's OnApplicationQuit.
    /// </summary>
    public static void Shutdown()
    {
        CSharpScriptManager.Instance.Shutdown();
    }

    /// <summary>
    /// Update all running scripts.
    /// Call this from your mod's OnUpdate.
    /// </summary>
    public static void UpdateScripts()
    {
        CSharpScriptManager.Instance.UpdateScripts();
    }

    /// <summary>
    /// Render GUI for all running scripts.
    /// Call this from your mod's OnGUI.
    /// </summary>
    public static void RenderScriptGUI()
    {
        CSharpScriptManager.Instance.RenderScriptGUI();
    }

    /// <summary>
    /// Start a script by name.
    /// </summary>
    public static bool StartScript(string scriptName)
    {
        return CSharpScriptManager.Instance.StartScript(scriptName);
    }

    /// <summary>
    /// Stop a script by name.
    /// </summary>
    public static bool StopScript(string scriptName)
    {
        return CSharpScriptManager.Instance.StopScript(scriptName);
    }

    /// <summary>
    /// Check if a script is running.
    /// </summary>
    public static bool IsScriptRunning(string scriptName)
    {
        return CSharpScriptManager.Instance.IsScriptRunning(scriptName);
    }

    /// <summary>
    /// Get a script instance by name.
    /// </summary>
    public static IScriptRoutine? GetScript(string scriptName)
    {
        return CSharpScriptManager.Instance.GetScript(scriptName);
    }

    /// <summary>
    /// Reload all scripts from disk.
    /// </summary>
    public static void ReloadAllScripts()
    {
        CSharpScriptManager.Instance.ReloadAllScripts();
    }

    /// <summary>
    /// Get list of available scripts.
    /// </summary>
    public static List<string> GetAvailableScripts()
    {
        return CSharpScriptManager.Instance.GetAvailableScripts();
    }

    /// <summary>
    /// Get list of loaded scripts.
    /// </summary>
    public static IReadOnlyDictionary<string, IScriptRoutine> GetLoadedScripts()
    {
        return CSharpScriptManager.Instance.LoadedScripts;
    }

    /// <summary>
    /// Get list of running scripts.
    /// </summary>
    public static IReadOnlySet<string> GetRunningScripts()
    {
        return CSharpScriptManager.Instance.RunningScripts;
    }
}

/*
 * USAGE EXAMPLE:
 * 
 * In your main mod class:
 * 
 * public override void OnApplicationStart()
 * {
 *     CSharpIntegration.Initialize();
 * }
 * 
 * public override void OnApplicationQuit()
 * {
 *     CSharpIntegration.Shutdown();
 * }
 * 
 * public override void OnUpdate()
 * {
 *     // Update all running scripts
 *     CSharpIntegration.UpdateScripts();
 *     
 *     // Reload scripts with F9
 *     if (Input.GetKeyDown(KeyCode.F9))
 *         CSharpIntegration.ReloadAllScripts();
 * }
 * 
 * public override void OnGUI()
 * {
 *     // Render GUI for all running scripts
 *     CSharpIntegration.RenderScriptGUI();
 * }
 */
