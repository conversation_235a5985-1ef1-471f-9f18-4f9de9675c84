using System.Collections;
using TestLE.Scripting;
using MelonLoader;
using UnityEngine;

namespace MyScriptLibrary;

/// <summary>
/// A simple GUI script that demonstrates window creation and user interaction.
/// </summary>
public class SimpleGUIScript : ScriptBase, IGuiScript
{
    public override string Name => "Simple GUI Script";

    private bool _showWindow = false;
    private string _userInput = "Type something here...";
    private int _clickCount = 0;

    public override bool CanExecute()
    {
        return true; // GUI scripts can always run
    }

    public override IEnumerator Execute()
    {
        Log("Simple GUI Script started! Press F2 to toggle the window.");
        _showWindow = true;
        
        // This script runs indefinitely until stopped
        while (true)
        {
            yield return new WaitForSeconds(0.1f);
        }
    }

    public void OnGUI()
    {
        // Check for F2 key to toggle window
        if (Event.current.type == EventType.KeyDown && 
            Event.current.keyCode == KeyCode.F2)
        {
            _showWindow = !_showWindow;
            Log($"GUI window {(_showWindow ? "opened" : "closed")}");
        }

        if (_showWindow)
        {
            // Create a simple window
            GUILayout.Window(54321, new Rect(200, 200, 350, 250), 
                (GUI.WindowFunction)DrawWindow, "My Script Library - Simple GUI");
        }
    }

    private void DrawWindow(int windowId)
    {
        GUILayout.Label("Welcome to My Script Library!");
        GUILayout.Space(10);
        
        GUILayout.Label($"Current Scene: {CurrentScene}");
        
        if (Player != null)
        {
            GUILayout.Label($"Player Position: {Player.transform?.position}");
        }
        else
        {
            GUILayout.Label("Player: Not found");
        }
        
        GUILayout.Space(10);
        
        GUILayout.Label("User Input:");
        _userInput = GUILayout.TextField(_userInput);
        
        GUILayout.Space(5);
        
        if (GUILayout.Button($"Click Me! (Clicked {_clickCount} times)"))
        {
            _clickCount++;
            Log($"Button clicked! Count: {_clickCount}, Input: '{_userInput}'");
        }
        
        GUILayout.Space(10);
        
        GUILayout.BeginHorizontal();
        
        if (GUILayout.Button("Log Info"))
        {
            Log($"Info logged at {System.DateTime.Now:HH:mm:ss}");
        }
        
        if (GUILayout.Button("Close Window"))
        {
            _showWindow = false;
            Log("Window closed via button");
        }
        
        GUILayout.EndHorizontal();
        
        GUILayout.Space(5);
        GUILayout.Label("Press F2 to toggle this window");
        
        // Make window draggable
        GUI.DragWindow();
    }

    public override void OnLoad()
    {
        base.OnLoad();
        _clickCount = 0;
        _showWindow = false;
        Log("Simple GUI Script loaded! Press F2 to open the window.");
    }

    public override void OnUnload()
    {
        Log("Simple GUI Script unloading. Window will be closed.");
        _showWindow = false;
        base.OnUnload();
    }
}
