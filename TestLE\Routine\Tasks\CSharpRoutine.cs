using System.Collections;
using <PERSON>onLoader;
using TestLE.Routine.Interfaces;
using TestLE.Scripting;

namespace TestLE.Routine.Tasks;

/// <summary>
/// A routine task that executes C# scripts with coroutine support.
/// Provides a bridge between the game's task system and compiled C# scripts.
/// </summary>
public class CSharpRoutine : IGameTask
{
    private readonly string _scriptName;
    private IScriptRoutine? _script;
    private IEnumerator? _currentCoroutine;

    public CSharpRoutine(string scriptName)
    {
        _scriptName = scriptName;
    }

    public bool CanExecute()
    {
        EnsureScriptLoaded();
        if (_script == null) return false;

        try
        {
            return _script.CanExecute();
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error in C# script CanExecute for {_scriptName}: {ex.Message}");
            return false;
        }
    }

    public IEnumerator Execute()
    {
        EnsureScriptLoaded();
        if (_script == null) yield break;

        // Execute the C# script's coroutine
        IEnumerator? scriptCoroutine = null;
        try
        {
            scriptCoroutine = _script.Execute();
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error executing C# script {_scriptName}: {ex.Message}");
            yield break;
        }

        if (scriptCoroutine != null)
        {
            _currentCoroutine = scriptCoroutine;
            yield return _currentCoroutine;
        }
        else
        {
            MelonLogger.Warning($"C# script {_scriptName}.Execute() returned null");
        }
    }

    public void Stop()
    {
        _currentCoroutine = null;
        // Note: We don't unload the script here as it might be used by other routines
        // The script manager handles script lifecycle
    }

    public void Reset()
    {
        _currentCoroutine = null;
        // Refresh script reference in case it was hot-reloaded
        _script = null;
    }

    private void EnsureScriptLoaded()
    {
        if (_script != null) return;

        if (!CSharpScriptManager.Instance.IsInitialized)
        {
            MelonLogger.Warning($"CSharpScriptManager not initialized when trying to load script: {_scriptName}");
            return;
        }

        _script = CSharpScriptManager.Instance.GetScript(_scriptName);
        if (_script == null)
        {
            // Script not found - it may be in a library that needs to be loaded
            // For DLL-based system, scripts are loaded when libraries are loaded
            MelonLogger.Warning($"Script '{_scriptName}' not found. Make sure the containing library is loaded.");
        }

        if (_script == null)
        {
            MelonLogger.Error($"Failed to load C# script: {_scriptName}");
        }
    }
}
