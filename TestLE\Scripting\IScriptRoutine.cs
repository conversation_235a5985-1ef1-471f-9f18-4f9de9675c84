using System.Collections;

namespace TestLE.Scripting;

/// <summary>
/// Interface that all C# script routines must implement.
/// Provides the basic contract for executable scripts with coroutine support.
/// </summary>
public interface IScriptRoutine
{
    /// <summary>
    /// Gets the name of this script routine.
    /// </summary>
    string Name { get; }

    /// <summary>
    /// Determines whether this routine can execute in the current game state.
    /// </summary>
    /// <returns>True if the routine can execute, false otherwise.</returns>
    bool CanExecute();

    /// <summary>
    /// Executes the routine as a coroutine.
    /// </summary>
    /// <returns>An IEnumerator for coroutine execution.</returns>
    IEnumerator Execute();

    /// <summary>
    /// Called when the script is first loaded or reloaded.
    /// Use this for initialization that should happen once per load.
    /// </summary>
    void OnLoad();

    /// <summary>
    /// Called when the script is being unloaded (before hot reload).
    /// Use this for cleanup.
    /// </summary>
    void OnUnload();
}

/// <summary>
/// Interface for scripts that need regular update calls.
/// </summary>
public interface IUpdatableScript
{
    /// <summary>
    /// Called every frame when the script is active.
    /// </summary>
    void OnUpdate();
}

/// <summary>
/// Interface for scripts that need GUI rendering.
/// </summary>
public interface IGuiScript
{
    /// <summary>
    /// Called during GUI rendering when the script is active.
    /// </summary>
    void OnGUI();
}
