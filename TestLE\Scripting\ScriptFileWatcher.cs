using MelonLoader;

namespace TestLE.Scripting;

/// <summary>
/// File system watcher for C# script files that triggers hot reloading.
/// Monitors .cs files in the scripts directory and notifies when changes occur.
/// </summary>
public class ScriptFileWatcher : IDisposable
{
    private readonly string _scriptsDirectory;
    private readonly FileSystemWatcher _fileWatcher;
    private readonly Dictionary<string, DateTime> _lastWriteTimes = new();
    private readonly object _lock = new();
    private bool _disposed = false;

    /// <summary>
    /// Event fired when a script file is changed.
    /// </summary>
    public event Action<string>? ScriptChanged;

    /// <summary>
    /// Event fired when a script file is created.
    /// </summary>
    public event Action<string>? ScriptCreated;

    /// <summary>
    /// Event fired when a script file is deleted.
    /// </summary>
    public event Action<string>? ScriptDeleted;

    /// <summary>
    /// Event fired when a script file is renamed.
    /// </summary>
    public event Action<string, string>? ScriptRenamed;

    public ScriptFileWatcher(string scriptsDirectory)
    {
        _scriptsDirectory = scriptsDirectory;
        
        // Ensure directory exists
        Directory.CreateDirectory(_scriptsDirectory);

        _fileWatcher = new FileSystemWatcher(_scriptsDirectory)
        {
            Filter = "*.cs",
            IncludeSubdirectories = false,
            NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.FileName | NotifyFilters.CreationTime
        };

        _fileWatcher.Changed += OnFileChanged;
        _fileWatcher.Created += OnFileCreated;
        _fileWatcher.Deleted += OnFileDeleted;
        _fileWatcher.Renamed += OnFileRenamed;
        _fileWatcher.Error += OnError;

        // Initialize last write times for existing files
        InitializeLastWriteTimes();
    }

    /// <summary>
    /// Start watching for file changes.
    /// </summary>
    public void StartWatching()
    {
        if (_disposed) return;

        try
        {
            _fileWatcher.EnableRaisingEvents = true;
            MelonLogger.Msg($"Started watching C# scripts in: {_scriptsDirectory}");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to start script file watcher: {ex.Message}");
        }
    }

    /// <summary>
    /// Stop watching for file changes.
    /// </summary>
    public void StopWatching()
    {
        if (_disposed) return;

        try
        {
            _fileWatcher.EnableRaisingEvents = false;
            MelonLogger.Msg("Stopped watching C# scripts");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error stopping script file watcher: {ex.Message}");
        }
    }

    /// <summary>
    /// Get list of available script files (without .cs extension).
    /// </summary>
    public List<string> GetAvailableScripts()
    {
        if (!Directory.Exists(_scriptsDirectory))
            return new List<string>();

        return Directory.GetFiles(_scriptsDirectory, "*.cs", SearchOption.TopDirectoryOnly)
            .Select(Path.GetFileNameWithoutExtension)
            .Where(name => !string.IsNullOrEmpty(name))
            .Cast<string>()
            .ToList();
    }

    /// <summary>
    /// Get the full path to a script file.
    /// </summary>
    public string GetScriptPath(string scriptName)
    {
        return Path.Combine(_scriptsDirectory, $"{scriptName}.cs");
    }

    /// <summary>
    /// Check if a script file exists.
    /// </summary>
    public bool ScriptExists(string scriptName)
    {
        return File.Exists(GetScriptPath(scriptName));
    }

    /// <summary>
    /// Read the content of a script file.
    /// </summary>
    public string? ReadScriptContent(string scriptName)
    {
        var scriptPath = GetScriptPath(scriptName);
        if (!File.Exists(scriptPath)) return null;

        try
        {
            return File.ReadAllText(scriptPath);
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to read script '{scriptName}': {ex.Message}");
            return null;
        }
    }

    private void InitializeLastWriteTimes()
    {
        lock (_lock)
        {
            _lastWriteTimes.Clear();
            
            if (!Directory.Exists(_scriptsDirectory)) return;

            var scriptFiles = Directory.GetFiles(_scriptsDirectory, "*.cs", SearchOption.TopDirectoryOnly);
            foreach (var filePath in scriptFiles)
            {
                try
                {
                    var lastWrite = File.GetLastWriteTime(filePath);
                    _lastWriteTimes[filePath] = lastWrite;
                }
                catch
                {
                    // Ignore files we can't access
                }
            }
        }
    }

    private void OnFileChanged(object sender, FileSystemEventArgs e)
    {
        if (_disposed || e.ChangeType != WatcherChangeTypes.Changed) return;

        // Debounce rapid file changes (common with editors that save multiple times)
        lock (_lock)
        {
            try
            {
                var lastWrite = File.GetLastWriteTime(e.FullPath);
                
                if (_lastWriteTimes.TryGetValue(e.FullPath, out var previousWrite))
                {
                    // Only process if the file was actually modified (not just accessed)
                    if (Math.Abs((lastWrite - previousWrite).TotalMilliseconds) < 100)
                        return;
                }

                _lastWriteTimes[e.FullPath] = lastWrite;
            }
            catch
            {
                // File might be locked or deleted, ignore
                return;
            }
        }

        var scriptName = Path.GetFileNameWithoutExtension(e.Name);
        if (!string.IsNullOrEmpty(scriptName))
        {
            MelonLogger.Msg($"Script file changed: {scriptName}");
            ScriptChanged?.Invoke(scriptName);
        }
    }

    private void OnFileCreated(object sender, FileSystemEventArgs e)
    {
        if (_disposed) return;

        var scriptName = Path.GetFileNameWithoutExtension(e.Name);
        if (!string.IsNullOrEmpty(scriptName))
        {
            lock (_lock)
            {
                try
                {
                    _lastWriteTimes[e.FullPath] = File.GetLastWriteTime(e.FullPath);
                }
                catch
                {
                    // Ignore
                }
            }

            MelonLogger.Msg($"Script file created: {scriptName}");
            ScriptCreated?.Invoke(scriptName);
        }
    }

    private void OnFileDeleted(object sender, FileSystemEventArgs e)
    {
        if (_disposed) return;

        var scriptName = Path.GetFileNameWithoutExtension(e.Name);
        if (!string.IsNullOrEmpty(scriptName))
        {
            lock (_lock)
            {
                _lastWriteTimes.Remove(e.FullPath);
            }

            MelonLogger.Msg($"Script file deleted: {scriptName}");
            ScriptDeleted?.Invoke(scriptName);
        }
    }

    private void OnFileRenamed(object sender, RenamedEventArgs e)
    {
        if (_disposed) return;

        var oldScriptName = Path.GetFileNameWithoutExtension(e.OldName);
        var newScriptName = Path.GetFileNameWithoutExtension(e.Name);

        if (!string.IsNullOrEmpty(oldScriptName) && !string.IsNullOrEmpty(newScriptName))
        {
            lock (_lock)
            {
                _lastWriteTimes.Remove(e.OldFullPath);
                try
                {
                    _lastWriteTimes[e.FullPath] = File.GetLastWriteTime(e.FullPath);
                }
                catch
                {
                    // Ignore
                }
            }

            MelonLogger.Msg($"Script file renamed: {oldScriptName} -> {newScriptName}");
            ScriptRenamed?.Invoke(oldScriptName, newScriptName);
        }
    }

    private void OnError(object sender, ErrorEventArgs e)
    {
        MelonLogger.Error($"Script file watcher error: {e.GetException().Message}");
    }

    public void Dispose()
    {
        if (_disposed) return;

        _disposed = true;
        
        try
        {
            _fileWatcher?.Dispose();
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error disposing script file watcher: {ex.Message}");
        }
    }
}
