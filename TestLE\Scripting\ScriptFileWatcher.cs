using MelonLoader;

namespace TestLE.Scripting;

/// <summary>
/// File system watcher for script library DLL files that triggers hot reloading.
/// Monitors .dll files in the script libraries directory and notifies when changes occur.
/// </summary>
public class ScriptLibraryWatcher : IDisposable
{
    private readonly string _librariesDirectory;
    private readonly FileSystemWatcher _fileWatcher;
    private readonly Dictionary<string, DateTime> _lastWriteTimes = new();
    private readonly object _lock = new();
    private bool _disposed = false;

    /// <summary>
    /// Event fired when a script library is changed.
    /// </summary>
    public event Action<string>? LibraryChanged;

    /// <summary>
    /// Event fired when a script library is created.
    /// </summary>
    public event Action<string>? LibraryCreated;

    /// <summary>
    /// Event fired when a script library is deleted.
    /// </summary>
    public event Action<string>? LibraryDeleted;

    /// <summary>
    /// Event fired when a script library is renamed.
    /// </summary>
    public event Action<string, string>? LibraryRenamed;

    public ScriptLibraryWatcher(string librariesDirectory)
    {
        _librariesDirectory = librariesDirectory;

        // Ensure directory exists
        Directory.CreateDirectory(_librariesDirectory);

        _fileWatcher = new FileSystemWatcher(_librariesDirectory)
        {
            Filter = "*.dll",
            IncludeSubdirectories = false,
            NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.FileName | NotifyFilters.CreationTime
        };

        _fileWatcher.Changed += OnFileChanged;
        _fileWatcher.Created += OnFileCreated;
        _fileWatcher.Deleted += OnFileDeleted;
        _fileWatcher.Renamed += OnFileRenamed;
        _fileWatcher.Error += OnError;

        // Initialize last write times for existing files
        InitializeLastWriteTimes();
    }

    /// <summary>
    /// Start watching for file changes.
    /// </summary>
    public void StartWatching()
    {
        if (_disposed) return;

        try
        {
            _fileWatcher.EnableRaisingEvents = true;
            MelonLogger.Msg($"Started watching script libraries in: {_librariesDirectory}");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to start script library watcher: {ex.Message}");
        }
    }

    /// <summary>
    /// Stop watching for file changes.
    /// </summary>
    public void StopWatching()
    {
        if (_disposed) return;

        try
        {
            _fileWatcher.EnableRaisingEvents = false;
            MelonLogger.Msg("Stopped watching script libraries");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error stopping script library watcher: {ex.Message}");
        }
    }

    /// <summary>
    /// Get list of available script libraries (without .dll extension).
    /// </summary>
    public List<string> GetAvailableLibraries()
    {
        if (!Directory.Exists(_librariesDirectory))
            return new List<string>();

        return Directory.GetFiles(_librariesDirectory, "*.dll", SearchOption.TopDirectoryOnly)
            .Select(Path.GetFileNameWithoutExtension)
            .Where(name => !string.IsNullOrEmpty(name))
            .Cast<string>()
            .ToList();
    }

    /// <summary>
    /// Get the full path to a script library file.
    /// </summary>
    public string GetLibraryPath(string libraryName)
    {
        return Path.Combine(_librariesDirectory, $"{libraryName}.dll");
    }

    /// <summary>
    /// Check if a script library exists.
    /// </summary>
    public bool LibraryExists(string libraryName)
    {
        return File.Exists(GetLibraryPath(libraryName));
    }

    /// <summary>
    /// Get the file info for a script library.
    /// </summary>
    public FileInfo? GetLibraryInfo(string libraryName)
    {
        var libraryPath = GetLibraryPath(libraryName);
        if (!File.Exists(libraryPath)) return null;

        try
        {
            return new FileInfo(libraryPath);
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to get info for library '{libraryName}': {ex.Message}");
            return null;
        }
    }

    private void InitializeLastWriteTimes()
    {
        lock (_lock)
        {
            _lastWriteTimes.Clear();

            if (!Directory.Exists(_librariesDirectory)) return;

            var libraryFiles = Directory.GetFiles(_librariesDirectory, "*.dll", SearchOption.TopDirectoryOnly);
            foreach (var filePath in libraryFiles)
            {
                try
                {
                    var lastWrite = File.GetLastWriteTime(filePath);
                    _lastWriteTimes[filePath] = lastWrite;
                }
                catch
                {
                    // Ignore files we can't access
                }
            }
        }
    }

    private void OnFileChanged(object sender, FileSystemEventArgs e)
    {
        if (_disposed || e.ChangeType != WatcherChangeTypes.Changed) return;

        // Debounce rapid file changes (common with editors that save multiple times)
        lock (_lock)
        {
            try
            {
                var lastWrite = File.GetLastWriteTime(e.FullPath);
                
                if (_lastWriteTimes.TryGetValue(e.FullPath, out var previousWrite))
                {
                    // Only process if the file was actually modified (not just accessed)
                    if (Math.Abs((lastWrite - previousWrite).TotalMilliseconds) < 100)
                        return;
                }

                _lastWriteTimes[e.FullPath] = lastWrite;
            }
            catch
            {
                // File might be locked or deleted, ignore
                return;
            }
        }

        var libraryName = Path.GetFileNameWithoutExtension(e.Name);
        if (!string.IsNullOrEmpty(libraryName))
        {
            MelonLogger.Msg($"Script library changed: {libraryName}");
            LibraryChanged?.Invoke(libraryName);
        }
    }

    private void OnFileCreated(object sender, FileSystemEventArgs e)
    {
        if (_disposed) return;

        var libraryName = Path.GetFileNameWithoutExtension(e.Name);
        if (!string.IsNullOrEmpty(libraryName))
        {
            lock (_lock)
            {
                try
                {
                    _lastWriteTimes[e.FullPath] = File.GetLastWriteTime(e.FullPath);
                }
                catch
                {
                    // Ignore
                }
            }

            MelonLogger.Msg($"Script library created: {libraryName}");
            LibraryCreated?.Invoke(libraryName);
        }
    }

    private void OnFileDeleted(object sender, FileSystemEventArgs e)
    {
        if (_disposed) return;

        var libraryName = Path.GetFileNameWithoutExtension(e.Name);
        if (!string.IsNullOrEmpty(libraryName))
        {
            lock (_lock)
            {
                _lastWriteTimes.Remove(e.FullPath);
            }

            MelonLogger.Msg($"Script library deleted: {libraryName}");
            LibraryDeleted?.Invoke(libraryName);
        }
    }

    private void OnFileRenamed(object sender, RenamedEventArgs e)
    {
        if (_disposed) return;

        var oldLibraryName = Path.GetFileNameWithoutExtension(e.OldName);
        var newLibraryName = Path.GetFileNameWithoutExtension(e.Name);

        if (!string.IsNullOrEmpty(oldLibraryName) && !string.IsNullOrEmpty(newLibraryName))
        {
            lock (_lock)
            {
                _lastWriteTimes.Remove(e.OldFullPath);
                try
                {
                    _lastWriteTimes[e.FullPath] = File.GetLastWriteTime(e.FullPath);
                }
                catch
                {
                    // Ignore
                }
            }

            MelonLogger.Msg($"Script library renamed: {oldLibraryName} -> {newLibraryName}");
            LibraryRenamed?.Invoke(oldLibraryName, newLibraryName);
        }
    }

    private void OnError(object sender, ErrorEventArgs e)
    {
        MelonLogger.Error($"Script library watcher error: {e.GetException().Message}");
    }

    public void Dispose()
    {
        if (_disposed) return;

        _disposed = true;
        
        try
        {
            _fileWatcher?.Dispose();
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error disposing script file watcher: {ex.Message}");
        }
    }
}
