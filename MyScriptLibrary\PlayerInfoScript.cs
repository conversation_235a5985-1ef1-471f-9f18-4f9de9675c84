using System.Collections;
using TestLE.Scripting;
using MelonLoader;
using UnityEngine;

namespace MyScriptLibrary;

/// <summary>
/// A script that displays player information and demonstrates game API usage.
/// </summary>
public class PlayerInfoScript : ScriptBase, IUpdatableScript
{
    public override string Name => "Player Info Script";

    private int _updateCounter = 0;

    public override bool CanExecute()
    {
        // Only run if we have a player
        return Player != null;
    }

    public override IEnumerator Execute()
    {
        Log("Player Info Script started!");
        Log($"Current scene: {CurrentScene}");
        
        if (Player != null)
        {
            Log($"Player position: {Player.transform?.position}");
            Log($"Player name: {Player.name}");
        }

        Log($"Found {Enemies.Count} enemies in the scene");
        Log($"Found {GroundItems.Count} ground items in the scene");
        Log($"Found {Interactables.Count} interactable objects in the scene");

        // Run for 10 seconds, logging info every 2 seconds
        for (int i = 0; i < 5; i++)
        {
            yield return new WaitForSeconds(2f);
            
            if (Player != null)
            {
                Log($"Update {i + 1}: Player at {Player.transform?.position}");
            }
        }

        Log("Player Info Script completed!");
    }

    public void OnUpdate()
    {
        // This runs every frame when the script is active
        _updateCounter++;
        
        // Log every 300 frames (roughly every 5 seconds at 60 FPS)
        if (_updateCounter % 300 == 0)
        {
            if (Player != null)
            {
                Log($"OnUpdate: Player position is {Player.transform?.position}");
            }
        }
    }

    public override void OnLoad()
    {
        base.OnLoad();
        _updateCounter = 0;
        Log("Player Info Script loaded and ready!");
    }

    public override void OnUnload()
    {
        Log($"Player Info Script unloading after {_updateCounter} updates");
        base.OnUnload();
    }
}
