# Script Library Template

This template provides a starting point for creating script libraries that can be hot-reloaded by the TestLE mod.

## Getting Started

1. **Copy this template folder** to create your own script library project
2. **Rename the project** by editing `ScriptLibraryTemplate.csproj`:
   - Change `<AssemblyName>MyScriptLibrary</AssemblyName>` to your desired library name
   - Change `<RootNamespace>MyScriptLibrary</RootNamespace>` to your desired namespace
3. **Update the TestLE path** in the project file if needed:
   - Modify `<TestLEModsPath>` to point to your game's Mods folder
4. **Create your scripts** by implementing the `IScriptRoutine` interface or inheriting from `ScriptBase`

## Script Types

### Basic Script (IScriptRoutine)
```csharp
public class MyScript : ScriptBase
{
    public override string Name => "My Script";
    
    public override bool CanExecute() => true;
    
    public override IEnumerator Execute()
    {
        Log("Script is running!");
        yield return new WaitForSeconds(1f);
        Log("Script completed!");
    }
}
```

### Updatable Script (IUpdatableScript)
```csharp
public class MyUpdatableScript : ScriptBase, IUpdatableScript
{
    public void OnUpdate()
    {
        // Called every frame when script is active
    }
}
```

### GUI Script (IGuiScript)
```csharp
public class MyGuiScript : ScriptBase, IGuiScript
{
    public void OnGUI()
    {
        // Called during GUI rendering when script is active
        GUILayout.Label("Hello from GUI!");
    }
}
```

## Building and Hot Reloading

1. **Build the project** using Visual Studio, Rider, or `dotnet build`
2. The DLL will be **automatically copied** to the ScriptLibraries folder
3. **TestLE will detect the change** and hot-reload the library
4. **All scripts in the library** will be reloaded automatically

## Available APIs

Scripts have access to:

- **Player**: Current player object (`LocalPlayer? Player`)
- **Enemies**: List of current enemies (`List<Enemy> Enemies`)
- **GroundItems**: List of ground items (`List<GroundItem> GroundItems`)
- **Interactables**: List of interactable objects (`List<WorldObjectClickListener> Interactables`)
- **CurrentScene**: Current scene name (`string CurrentScene`)
- **Logging**: `Log()`, `LogWarning()`, `LogError()` methods

## Tips

- **Use coroutines** for any long-running operations
- **Check CanExecute()** to ensure your script only runs when appropriate
- **Handle exceptions** in your scripts to prevent crashes
- **Use meaningful names** for your scripts and libraries
- **Test hot reloading** by making small changes and rebuilding

## Debugging

- Use **MelonLogger** for logging: `Log("Debug message")`
- **Attach debugger** to the game process for full debugging support
- **Check the console** for compilation errors and runtime exceptions
- **Use try-catch blocks** around potentially failing code

## Example Scripts

See `ExampleScript.cs` for working examples of:
- Basic script execution with coroutines
- Update loop handling
- GUI window creation
- Input handling
- Game API usage
