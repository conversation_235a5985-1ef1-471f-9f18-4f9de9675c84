<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

    <!-- ILRepack disabled - using separate DLLs instead for better compatibility -->
    <!--
    <Target Name="ILRepacker" AfterTargets="Build">
        <ItemGroup>
            <InputAssemblies Include="$(OutputPath)$(AssemblyName).dll" />
        </ItemGroup>
        <ILRepack
                InputAssemblies="@(InputAssemblies)"
                OutputFile="$(OutputPath)$(AssemblyName).Merged.dll"
                TargetKind="Dll"
                Internalize="true"
                XmlDocumentation="true"
                DebugInfo="true"
                LibraryPath="$(OutputPath)"
                Parallel="true"
                Verbose="true" />
        <Copy SourceFiles="$(OutputPath)$(AssemblyName).Merged.dll"
              DestinationFiles="$(OutputPath)$(AssemblyName).dll"
              OverwriteReadOnlyFiles="true" />
    </Target>
    -->

    <!-- Copy DLL and all dependencies to Mods folder -->
    <Target Name="PostBuild" AfterTargets="Build" Inputs="@(Compile)" Outputs="$(MSBuildProjectFile).force">
        <!-- Copy the main DLL -->
        <Copy SourceFiles="$(OutputPath)$(AssemblyName).dll"
              DestinationFolder="H:\SteamLibrary\steamapps\common\Last Epoch - Copy\Mods\"
              SkipUnchangedFiles="false" />

        <!-- Copy all dependencies separately -->
        <ItemGroup>
            <DependencyDlls Include="$(OutputPath)Microsoft.CodeAnalysis.dll" />
            <DependencyDlls Include="$(OutputPath)Microsoft.CodeAnalysis.CSharp.dll" />
            <DependencyDlls Include="$(OutputPath)Microsoft.CodeAnalysis.CSharp.Workspaces.dll" />
            <DependencyDlls Include="$(OutputPath)Microsoft.CodeAnalysis.Workspaces.dll" />
            <DependencyDlls Include="$(OutputPath)System.Composition.AttributedModel.dll" />
            <DependencyDlls Include="$(OutputPath)System.Composition.Convention.dll" />
            <DependencyDlls Include="$(OutputPath)System.Composition.Hosting.dll" />
            <DependencyDlls Include="$(OutputPath)System.Composition.Runtime.dll" />
            <DependencyDlls Include="$(OutputPath)System.Composition.TypedParts.dll" />
            <DependencyDlls Include="$(OutputPath)System.IO.Pipelines.dll" />
            <DependencyDlls Include="$(OutputPath)Humanizer.dll" />
        </ItemGroup>

        <Copy SourceFiles="@(DependencyDlls)"
              DestinationFolder="H:\SteamLibrary\steamapps\common\Last Epoch - Copy\Mods\"
              SkipUnchangedFiles="false" />

        <!-- Copy Scripts folder to TestLE subfolder in Mods -->
        <ItemGroup>
            <ScriptFiles Include="Scripts\**\*" />
        </ItemGroup>

        <Copy SourceFiles="@(ScriptFiles)"
              DestinationFolder="H:\SteamLibrary\steamapps\common\Last Epoch - Copy\Mods\TestLE\Scripts\%(RecursiveDir)"
              SkipUnchangedFiles="false" />
    </Target>

</Project>
