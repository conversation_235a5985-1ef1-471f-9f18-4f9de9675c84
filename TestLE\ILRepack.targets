<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

    <!-- ILRepack disabled - using separate DLLs instead for better compatibility -->
    <!--
    <Target Name="ILRepacker" AfterTargets="Build">
        <ItemGroup>
            <InputAssemblies Include="$(OutputPath)$(AssemblyName).dll" />
        </ItemGroup>
        <ILRepack
                InputAssemblies="@(InputAssemblies)"
                OutputFile="$(OutputPath)$(AssemblyName).Merged.dll"
                TargetKind="Dll"
                Internalize="true"
                XmlDocumentation="true"
                DebugInfo="true"
                LibraryPath="$(OutputPath)"
                Parallel="true"
                Verbose="true" />
        <Copy SourceFiles="$(OutputPath)$(AssemblyName).Merged.dll"
              DestinationFiles="$(OutputPath)$(AssemblyName).dll"
              OverwriteReadOnlyFiles="true" />
    </Target>
    -->

    <!-- Copy DLL and all dependencies to Mods folder -->
    <Target Name="PostBuild" AfterTargets="Build" Inputs="@(Compile)" Outputs="$(MSBuildProjectFile).force">
        <!-- Copy the main DLL -->
        <Copy SourceFiles="$(OutputPath)$(AssemblyName).dll"
              DestinationFolder="H:\SteamLibrary\steamapps\common\Last Epoch - Copy\Mods\"
              SkipUnchangedFiles="false" />

        <!-- Copy remaining dependencies if any exist -->
        <ItemGroup>
            <!-- Add any remaining dependencies here if needed -->
        </ItemGroup>

        <!-- Copy legacy Scripts folder if it exists (for backward compatibility) -->
        <ItemGroup>
            <ScriptFiles Include="Scripts\**\*" Condition="Exists('Scripts')" />
        </ItemGroup>

        <Copy SourceFiles="@(ScriptFiles)"
              DestinationFolder="H:\SteamLibrary\steamapps\common\Last Epoch - Copy\Mods\TestLE\Scripts\%(RecursiveDir)"
              SkipUnchangedFiles="false"
              Condition="'@(ScriptFiles)' != ''" />

        <!-- Copy ScriptLibraryTemplate to TestLE subfolder in Mods -->
        <ItemGroup>
            <TemplateFiles Include="ScriptLibraryTemplate\**\*" />
        </ItemGroup>

        <Copy SourceFiles="@(TemplateFiles)"
              DestinationFolder="H:\SteamLibrary\steamapps\common\Last Epoch - Copy\Mods\TestLE\ScriptLibraryTemplate\%(RecursiveDir)"
              SkipUnchangedFiles="false" />

        <!-- Create ScriptLibraries directory -->
        <MakeDir Directories="H:\SteamLibrary\steamapps\common\Last Epoch - Copy\Mods\TestLE\ScriptLibraries" />
    </Target>

</Project>
