using MelonLoader;
using UnityEngine;

namespace TestLE.Scripting;

/// <summary>
/// UI for managing C# script libraries and scripts - select, start, stop, and reload.
/// </summary>
public class CSharpScriptUI
{
    private string? _selectedScript;
    private List<string> _availableScripts = new();
    private List<string> _availableLibraries = new();
    private int _selectedScriptIndex = -1;
    private int _selectedLibraryIndex = -1;
    private float _lastRefresh;
    private const float REFRESH_INTERVAL = 2f; // Refresh lists every 2 seconds
    private bool _showLibraries = true;

    /// <summary>
    /// Draw the C# script UI. Call this from your main OnGUI method.
    /// </summary>
    public void DrawUI()
    {
        if (!CSharpScriptManager.Instance.IsInitialized)
        {
            GUILayout.Label("C# Script Manager not initialized", GUILayout.Height(20));
            return;
        }

        // Refresh script list periodically
        if (Time.time - _lastRefresh > REFRESH_INTERVAL)
        {
            RefreshScriptList();
            _lastRefresh = Time.time;
        }

        GUILayout.BeginVertical("box");
        GUILayout.Label("C# Script Library Control", GUILayout.Height(20));

        // Tab selection
        DrawTabSelection();

        if (_showLibraries)
        {
            // Library management
            DrawLibraryManagement();
        }
        else
        {
            // Script selection dropdown
            DrawScriptSelection();

            // Start/Stop controls
            DrawScriptControls();
        }

        // Reload controls
        DrawReloadControls();

        // Status display
        DrawStatus();

        GUILayout.EndVertical();
    }

    private void RefreshScriptList()
    {
        var newScripts = CSharpScriptManager.Instance.GetAvailableScripts();
        var newLibraries = CSharpScriptManager.Instance.GetAvailableLibraries();

        // Update scripts list
        if (!newScripts.SequenceEqual(_availableScripts))
        {
            _availableScripts = newScripts;

            // Update selected script index if needed
            if (!string.IsNullOrEmpty(_selectedScript))
            {
                _selectedScriptIndex = _availableScripts.IndexOf(_selectedScript);
                if (_selectedScriptIndex == -1)
                {
                    _selectedScript = null;
                }
            }
        }

        // Update libraries list
        if (!newLibraries.SequenceEqual(_availableLibraries))
        {
            _availableLibraries = newLibraries;

            // Reset selected library index if list changed
            if (_selectedLibraryIndex >= _availableLibraries.Count)
            {
                _selectedLibraryIndex = -1;
            }
        }
    }

    private void DrawTabSelection()
    {
        GUILayout.BeginHorizontal();

        var librariesStyle = _showLibraries ? GUI.skin.button : GUI.skin.box;
        var scriptsStyle = !_showLibraries ? GUI.skin.button : GUI.skin.box;

        if (GUILayout.Button("Libraries", librariesStyle, GUILayout.Height(25)))
        {
            _showLibraries = true;
        }

        if (GUILayout.Button("Scripts", scriptsStyle, GUILayout.Height(25)))
        {
            _showLibraries = false;
        }

        GUILayout.EndHorizontal();
        GUILayout.Space(5);
    }

    private void DrawLibraryManagement()
    {
        GUILayout.BeginHorizontal();
        GUILayout.Label("Library:", GUILayout.Width(50));

        if (_availableLibraries.Count == 0)
        {
            GUILayout.Label("No libraries found", GUILayout.Height(25));
        }
        else
        {
            var libraryNames = _availableLibraries.ToArray();
            _selectedLibraryIndex = EditorGUILayout.Popup(_selectedLibraryIndex, libraryNames, GUILayout.Height(25));
        }

        GUILayout.EndHorizontal();
        GUILayout.Space(5);

        // Library controls
        GUILayout.BeginHorizontal();

        if (_selectedLibraryIndex >= 0 && _selectedLibraryIndex < _availableLibraries.Count)
        {
            var selectedLibrary = _availableLibraries[_selectedLibraryIndex];

            if (GUILayout.Button("Reload Library", GUILayout.Height(25)))
            {
                CSharpScriptManager.Instance.LoadLibrary(selectedLibrary);
            }

            if (GUILayout.Button("Unload Library", GUILayout.Height(25)))
            {
                CSharpScriptManager.Instance.UnloadLibrary(selectedLibrary);
            }
        }

        GUILayout.EndHorizontal();
        GUILayout.Space(5);

        // Show scripts in selected library
        if (_selectedLibraryIndex >= 0 && _selectedLibraryIndex < _availableLibraries.Count)
        {
            var selectedLibrary = _availableLibraries[_selectedLibraryIndex];
            var libraryScripts = _availableScripts.Where(s => s.StartsWith($"{selectedLibrary}.")).ToList();

            if (libraryScripts.Count > 0)
            {
                GUILayout.Label($"Scripts in {selectedLibrary}:");
                foreach (var script in libraryScripts)
                {
                    GUILayout.BeginHorizontal();
                    var scriptName = script.Substring(selectedLibrary.Length + 1); // Remove library prefix
                    GUILayout.Label($"  • {scriptName}", GUILayout.Width(200));

                    var isRunning = CSharpScriptManager.Instance.IsScriptRunning(script);
                    var buttonText = isRunning ? "Stop" : "Start";
                    var buttonColor = isRunning ? Color.red : Color.green;

                    var oldColor = GUI.backgroundColor;
                    GUI.backgroundColor = buttonColor;

                    if (GUILayout.Button(buttonText, GUILayout.Width(60), GUILayout.Height(20)))
                    {
                        if (isRunning)
                        {
                            CSharpScriptManager.Instance.StopScript(script);
                        }
                        else
                        {
                            CSharpScriptManager.Instance.StartScript(script);
                        }
                    }

                    GUI.backgroundColor = oldColor;
                    GUILayout.EndHorizontal();
                }
            }
            else
            {
                GUILayout.Label($"No scripts found in {selectedLibrary}");
            }
        }
    }

    private void DrawScriptSelection()
    {
        GUILayout.BeginHorizontal();
        GUILayout.Label("Script:", GUILayout.Width(50));

        if (_availableScripts.Count == 0)
        {
            GUILayout.Label("No scripts found", GUILayout.Height(25));
        }
        else
        {
            var scriptNames = _availableScripts.ToArray();
            var newIndex = EditorGUILayout.Popup(_selectedScriptIndex, scriptNames, GUILayout.Height(25));
            
            if (newIndex != _selectedScriptIndex && newIndex >= 0 && newIndex < scriptNames.Length)
            {
                _selectedScriptIndex = newIndex;
                _selectedScript = scriptNames[newIndex];
            }
        }

        GUILayout.EndHorizontal();
    }

    private void DrawScriptControls()
    {
        GUILayout.BeginHorizontal();

        var isRunning = !string.IsNullOrEmpty(_selectedScript) && 
                       CSharpScriptManager.Instance.IsScriptRunning(_selectedScript);

        // Start button
        GUI.enabled = !isRunning && !string.IsNullOrEmpty(_selectedScript);
        if (GUILayout.Button("Start", GUILayout.Width(60), GUILayout.Height(25)))
        {
            if (!string.IsNullOrEmpty(_selectedScript))
                CSharpScriptManager.Instance.StartScript(_selectedScript);
        }

        // Stop button
        GUI.enabled = isRunning;
        if (GUILayout.Button("Stop", GUILayout.Width(60), GUILayout.Height(25)))
        {
            if (!string.IsNullOrEmpty(_selectedScript))
                CSharpScriptManager.Instance.StopScript(_selectedScript);
        }

        GUI.enabled = true;
        GUILayout.EndHorizontal();
    }

    private void DrawReloadControls()
    {
        GUILayout.BeginHorizontal();

        if (_showLibraries)
        {
            // Reload selected library
            GUI.enabled = _selectedLibraryIndex >= 0 && _selectedLibraryIndex < _availableLibraries.Count;
            if (GUILayout.Button("Reload Library", GUILayout.Width(100), GUILayout.Height(25)))
            {
                var selectedLibrary = _availableLibraries[_selectedLibraryIndex];
                MelonLogger.Msg($"Manually reloading library: {selectedLibrary}");
                CSharpScriptManager.Instance.LoadLibrary(selectedLibrary);
            }
        }
        else
        {
            // Individual script reloading not supported with DLL-based system
            GUI.enabled = false;
            if (GUILayout.Button("Reload Script", GUILayout.Width(100), GUILayout.Height(25)))
            {
                // Individual scripts cannot be reloaded - entire library must be reloaded
            }
        }

        // Reload all libraries
        GUI.enabled = true;
        if (GUILayout.Button("Reload All", GUILayout.Width(80), GUILayout.Height(25)))
        {
            MelonLogger.Msg("Manually reloading all libraries");
            CSharpScriptManager.Instance.ReloadAllScripts();
        }

        GUILayout.EndHorizontal();
    }

    private void DrawStatus()
    {
        GUILayout.Space(5);
        
        // Show selected script status
        if (!string.IsNullOrEmpty(_selectedScript))
        {
            var isLoaded = CSharpScriptManager.Instance.GetScript(_selectedScript) != null;
            var isRunning = CSharpScriptManager.Instance.IsScriptRunning(_selectedScript);
            
            var status = isLoaded ? (isRunning ? "Running" : "Loaded") : "Not Loaded";
            var color = isRunning ? Color.green : (isLoaded ? Color.yellow : Color.red);
            
            var oldColor = GUI.color;
            GUI.color = color;
            GUILayout.Label($"Status: {status}", GUILayout.Height(20));
            GUI.color = oldColor;
        }

        // Show overall statistics
        var loadedCount = CSharpScriptManager.Instance.LoadedScripts.Count;
        var runningCount = CSharpScriptManager.Instance.RunningScripts.Count;
        var availableCount = _availableScripts.Count;
        var libraryCount = _availableLibraries.Count;

        GUILayout.Label($"Libraries: {libraryCount} available", GUILayout.Height(20));
        GUILayout.Label($"Scripts: {loadedCount}/{availableCount} loaded, {runningCount} running",
                       GUILayout.Height(20));
    }
}

/// <summary>
/// Helper class for EditorGUILayout.Popup since we're not in the editor.
/// Provides a simple popup implementation using GUI.
/// </summary>
public static class EditorGUILayout
{
    private static readonly Dictionary<int, bool> _popupStates = new();
    private static int _controlId = 0;

    public static int Popup(int selectedIndex, string[] displayedOptions, params GUILayoutOption[] options)
    {
        var controlId = ++_controlId;
        
        if (displayedOptions.Length == 0) return -1;
        
        var selectedText = selectedIndex >= 0 && selectedIndex < displayedOptions.Length 
            ? displayedOptions[selectedIndex] 
            : "None";

        if (GUILayout.Button(selectedText, options))
        {
            _popupStates[controlId] = !_popupStates.GetValueOrDefault(controlId, false);
        }

        if (_popupStates.GetValueOrDefault(controlId, false))
        {
            GUILayout.BeginVertical("box");
            for (int i = 0; i < displayedOptions.Length; i++)
            {
                if (GUILayout.Button(displayedOptions[i], GUILayout.Height(20)))
                {
                    _popupStates[controlId] = false;
                    return i;
                }
            }
            GUILayout.EndVertical();
        }

        return selectedIndex;
    }
}
