using MelonLoader;
using UnityEngine;

namespace TestLE.Scripting;

/// <summary>
/// UI for managing C# scripts - select, start, stop, and reload scripts.
/// </summary>
public class CSharpScriptUI
{
    private string? _selectedScript;
    private List<string> _availableScripts = new();
    private int _selectedScriptIndex = -1;
    private float _lastRefresh;
    private const float REFRESH_INTERVAL = 2f; // Refresh script list every 2 seconds

    /// <summary>
    /// Draw the C# script UI. Call this from your main OnGUI method.
    /// </summary>
    public void DrawUI()
    {
        if (!CSharpScriptManager.Instance.IsInitialized)
        {
            GUILayout.Label("C# Script Manager not initialized", GUILayout.Height(20));
            return;
        }

        // Refresh script list periodically
        if (Time.time - _lastRefresh > REFRESH_INTERVAL)
        {
            RefreshScriptList();
            _lastRefresh = Time.time;
        }

        GUILayout.BeginVertical("box");
        GUILayout.Label("C# Script Control", GUILayout.Height(20));

        // Script selection dropdown
        DrawScriptSelection();

        // Start/Stop controls
        DrawScriptControls();

        // Reload controls
        DrawReloadControls();

        // Status display
        DrawStatus();

        GUILayout.EndVertical();
    }

    private void RefreshScriptList()
    {
        var newScripts = CSharpScriptManager.Instance.GetAvailableScripts();
        
        // Only update if the list actually changed
        if (!newScripts.SequenceEqual(_availableScripts))
        {
            _availableScripts = newScripts;
            
            // Update selected script index if needed
            if (!string.IsNullOrEmpty(_selectedScript))
            {
                _selectedScriptIndex = _availableScripts.IndexOf(_selectedScript);
                if (_selectedScriptIndex == -1)
                {
                    _selectedScript = null;
                }
            }
        }
    }

    private void DrawScriptSelection()
    {
        GUILayout.BeginHorizontal();
        GUILayout.Label("Script:", GUILayout.Width(50));

        if (_availableScripts.Count == 0)
        {
            GUILayout.Label("No scripts found", GUILayout.Height(25));
        }
        else
        {
            var scriptNames = _availableScripts.ToArray();
            var newIndex = EditorGUILayout.Popup(_selectedScriptIndex, scriptNames, GUILayout.Height(25));
            
            if (newIndex != _selectedScriptIndex && newIndex >= 0 && newIndex < scriptNames.Length)
            {
                _selectedScriptIndex = newIndex;
                _selectedScript = scriptNames[newIndex];
            }
        }

        GUILayout.EndHorizontal();
    }

    private void DrawScriptControls()
    {
        GUILayout.BeginHorizontal();

        var isRunning = !string.IsNullOrEmpty(_selectedScript) && 
                       CSharpScriptManager.Instance.IsScriptRunning(_selectedScript);

        // Start button
        GUI.enabled = !isRunning && !string.IsNullOrEmpty(_selectedScript);
        if (GUILayout.Button("Start", GUILayout.Width(60), GUILayout.Height(25)))
        {
            if (!string.IsNullOrEmpty(_selectedScript))
                CSharpScriptManager.Instance.StartScript(_selectedScript);
        }

        // Stop button
        GUI.enabled = isRunning;
        if (GUILayout.Button("Stop", GUILayout.Width(60), GUILayout.Height(25)))
        {
            if (!string.IsNullOrEmpty(_selectedScript))
                CSharpScriptManager.Instance.StopScript(_selectedScript);
        }

        GUI.enabled = true;
        GUILayout.EndHorizontal();
    }

    private void DrawReloadControls()
    {
        GUILayout.BeginHorizontal();

        // Reload selected script
        GUI.enabled = !string.IsNullOrEmpty(_selectedScript);
        if (GUILayout.Button("Reload Script", GUILayout.Width(100), GUILayout.Height(25)))
        {
            if (!string.IsNullOrEmpty(_selectedScript))
            {
                MelonLogger.Msg($"Manually reloading script: {_selectedScript}");
                CSharpScriptManager.Instance.LoadScript(_selectedScript);
            }
        }

        // Reload all scripts
        GUI.enabled = true;
        if (GUILayout.Button("Reload All", GUILayout.Width(80), GUILayout.Height(25)))
        {
            MelonLogger.Msg("Manually reloading all scripts");
            CSharpScriptManager.Instance.ReloadAllScripts();
        }

        GUILayout.EndHorizontal();
    }

    private void DrawStatus()
    {
        GUILayout.Space(5);
        
        // Show selected script status
        if (!string.IsNullOrEmpty(_selectedScript))
        {
            var isLoaded = CSharpScriptManager.Instance.GetScript(_selectedScript) != null;
            var isRunning = CSharpScriptManager.Instance.IsScriptRunning(_selectedScript);
            
            var status = isLoaded ? (isRunning ? "Running" : "Loaded") : "Not Loaded";
            var color = isRunning ? Color.green : (isLoaded ? Color.yellow : Color.red);
            
            var oldColor = GUI.color;
            GUI.color = color;
            GUILayout.Label($"Status: {status}", GUILayout.Height(20));
            GUI.color = oldColor;
        }

        // Show overall statistics
        var loadedCount = CSharpScriptManager.Instance.LoadedScripts.Count;
        var runningCount = CSharpScriptManager.Instance.RunningScripts.Count;
        var availableCount = _availableScripts.Count;
        
        GUILayout.Label($"Scripts: {loadedCount}/{availableCount} loaded, {runningCount} running", 
                       GUILayout.Height(20));

        // Show compilation status
        var compiledCount = CSharpCompiler.Instance.CompiledScripts.Count;
        GUILayout.Label($"Compiled: {compiledCount}", GUILayout.Height(20));
    }
}

/// <summary>
/// Helper class for EditorGUILayout.Popup since we're not in the editor.
/// Provides a simple popup implementation using GUI.
/// </summary>
public static class EditorGUILayout
{
    private static readonly Dictionary<int, bool> _popupStates = new();
    private static int _controlId = 0;

    public static int Popup(int selectedIndex, string[] displayedOptions, params GUILayoutOption[] options)
    {
        var controlId = ++_controlId;
        
        if (displayedOptions.Length == 0) return -1;
        
        var selectedText = selectedIndex >= 0 && selectedIndex < displayedOptions.Length 
            ? displayedOptions[selectedIndex] 
            : "None";

        if (GUILayout.Button(selectedText, options))
        {
            _popupStates[controlId] = !_popupStates.GetValueOrDefault(controlId, false);
        }

        if (_popupStates.GetValueOrDefault(controlId, false))
        {
            GUILayout.BeginVertical("box");
            for (int i = 0; i < displayedOptions.Length; i++)
            {
                if (GUILayout.Button(displayedOptions[i], GUILayout.Height(20)))
                {
                    _popupStates[controlId] = false;
                    return i;
                }
            }
            GUILayout.EndVertical();
        }

        return selectedIndex;
    }
}
