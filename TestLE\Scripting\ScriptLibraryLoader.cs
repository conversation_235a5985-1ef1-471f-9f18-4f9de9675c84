using System.Reflection;
using System.Runtime.Loader;
using MelonLoader;

namespace TestLE.Scripting;

/// <summary>
/// Manages loading and unloading of script library DLLs with hot reloading support.
/// Uses AssemblyLoadContext to enable proper assembly unloading for hot reloading.
/// </summary>
public class ScriptLibraryLoader : IDisposable
{
    private readonly Dictionary<string, LoadedLibrary> _loadedLibraries = new();
    private readonly object _lock = new();
    private bool _disposed = false;

    /// <summary>
    /// Event fired when a library is successfully loaded.
    /// </summary>
    public event Action<string, LoadedLibrary>? LibraryLoaded;

    /// <summary>
    /// Event fired when a library is unloaded.
    /// </summary>
    public event Action<string>? LibraryUnloaded;

    /// <summary>
    /// Event fired when a library fails to load.
    /// </summary>
    public event Action<string, Exception>? LibraryLoadFailed;

    public IReadOnlyDictionary<string, LoadedLibrary> LoadedLibraries => _loadedLibraries;

    /// <summary>
    /// Load a script library from the specified file path.
    /// </summary>
    public bool LoadLibrary(string libraryName, string filePath)
    {
        if (_disposed) return false;

        lock (_lock)
        {
            try
            {
                // Unload existing library if it exists
                if (_loadedLibraries.ContainsKey(libraryName))
                {
                    UnloadLibraryInternal(libraryName);
                }

                // Wait a bit to ensure file operations are complete
                Thread.Sleep(100);

                // Try to load the assembly with retry logic for file locking
                Assembly assembly = null;
                ScriptLibraryLoadContext loadContext = null;

                for (int attempt = 1; attempt <= 3; attempt++)
                {
                    try
                    {
                        // Create a new load context for this library
                        loadContext = new ScriptLibraryLoadContext(libraryName);

                        // Load the assembly
                        assembly = loadContext.LoadFromAssemblyPath(filePath);
                        break; // Success
                    }
                    catch (IOException ex) when (attempt < 3)
                    {
                        MelonLogger.Warning($"Attempt {attempt} to load library '{libraryName}' failed (file may be locked): {ex.Message}");
                        loadContext?.Unload();
                        loadContext = null;
                        Thread.Sleep(500); // Wait before retry
                    }
                }

                if (assembly == null)
                {
                    throw new InvalidOperationException($"Failed to load assembly after 3 attempts");
                }

                // Discover script types in the assembly
                var scriptTypes = DiscoverScriptTypes(assembly);

                var loadedLibrary = new LoadedLibrary(
                    libraryName,
                    assembly,
                    loadContext,
                    filePath,
                    scriptTypes
                );

                _loadedLibraries[libraryName] = loadedLibrary;

                MelonLogger.Msg($"Loaded script library: {libraryName} ({scriptTypes.Count} script types)");
                LibraryLoaded?.Invoke(libraryName, loadedLibrary);

                // Clean up old versioned DLL files
                CleanupOldVersions(libraryName, filePath);

                return true;
            }
            catch (Exception ex)
            {
                MelonLogger.Error($"Failed to load script library '{libraryName}': {ex.Message}");
                LibraryLoadFailed?.Invoke(libraryName, ex);
                return false;
            }
        }
    }

    /// <summary>
    /// Unload a script library.
    /// </summary>
    public bool UnloadLibrary(string libraryName)
    {
        if (_disposed) return false;

        lock (_lock)
        {
            return UnloadLibraryInternal(libraryName);
        }
    }

    /// <summary>
    /// Get a loaded library by name.
    /// </summary>
    public LoadedLibrary? GetLibrary(string libraryName)
    {
        lock (_lock)
        {
            return _loadedLibraries.TryGetValue(libraryName, out var library) ? library : null;
        }
    }

    /// <summary>
    /// Get all script types from all loaded libraries.
    /// </summary>
    public List<Type> GetAllScriptTypes()
    {
        lock (_lock)
        {
            var allTypes = new List<Type>();
            foreach (var library in _loadedLibraries.Values)
            {
                allTypes.AddRange(library.ScriptTypes);
            }
            return allTypes;
        }
    }

    /// <summary>
    /// Create an instance of a script type.
    /// </summary>
    public T? CreateScriptInstance<T>(Type scriptType) where T : class
    {
        try
        {
            if (!typeof(T).IsAssignableFrom(scriptType))
                return null;

            return Activator.CreateInstance(scriptType) as T;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to create instance of script type '{scriptType.Name}': {ex.Message}");
            return null;
        }
    }

    private bool UnloadLibraryInternal(string libraryName)
    {
        if (!_loadedLibraries.TryGetValue(libraryName, out var library))
            return false;

        try
        {
            // Remove from dictionary first to prevent further access
            _loadedLibraries.Remove(libraryName);

            // Dispose the library (this will unload the AssemblyLoadContext)
            library.Dispose();

            // Force garbage collection to help release assembly references
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            MelonLogger.Msg($"Unloaded script library: {libraryName}");
            LibraryUnloaded?.Invoke(libraryName);

            return true;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error unloading script library '{libraryName}': {ex.Message}");
            return false;
        }
    }

    private List<Type> DiscoverScriptTypes(Assembly assembly)
    {
        var scriptTypes = new List<Type>();
        
        try
        {
            var types = assembly.GetTypes();
            foreach (var type in types)
            {
                // Check if type implements IScriptRoutine and is not abstract
                if (typeof(IScriptRoutine).IsAssignableFrom(type) && 
                    !type.IsAbstract && 
                    !type.IsInterface)
                {
                    scriptTypes.Add(type);
                }
            }
        }
        catch (Exception ex)
        {
            MelonLogger.Warning($"Error discovering script types in assembly '{assembly.FullName}': {ex.Message}");
        }

        return scriptTypes;
    }

    private void CleanupOldVersions(string libraryName, string currentFilePath)
    {
        try
        {
            var directory = Path.GetDirectoryName(currentFilePath);
            if (string.IsNullOrEmpty(directory)) return;

            var pattern = $"{libraryName}_*.dll";
            var oldFiles = Directory.GetFiles(directory, pattern);

            foreach (var oldFile in oldFiles)
            {
                // Don't delete the current file
                if (string.Equals(oldFile, currentFilePath, StringComparison.OrdinalIgnoreCase))
                    continue;

                try
                {
                    File.Delete(oldFile);
                    MelonLogger.Msg($"Cleaned up old version: {Path.GetFileName(oldFile)}");
                }
                catch (Exception ex)
                {
                    // File might still be in use, that's OK
                    MelonLogger.Warning($"Could not delete old version {Path.GetFileName(oldFile)}: {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            MelonLogger.Warning($"Error during cleanup for {libraryName}: {ex.Message}");
        }
    }

    public void Dispose()
    {
        if (_disposed) return;

        lock (_lock)
        {
            // Unload all libraries
            var libraryNames = _loadedLibraries.Keys.ToList();
            foreach (var libraryName in libraryNames)
            {
                UnloadLibraryInternal(libraryName);
            }

            _disposed = true;
        }
    }
}

/// <summary>
/// Custom AssemblyLoadContext for script libraries to enable unloading.
/// </summary>
public class ScriptLibraryLoadContext : AssemblyLoadContext
{
    public ScriptLibraryLoadContext(string name) : base(name, isCollectible: true)
    {
    }

    protected override Assembly? Load(AssemblyName assemblyName)
    {
        // Let the default context handle loading of system assemblies
        return null;
    }
}

/// <summary>
/// Represents a loaded script library.
/// </summary>
public class LoadedLibrary : IDisposable
{
    public string Name { get; }
    public Assembly Assembly { get; }
    public ScriptLibraryLoadContext LoadContext { get; }
    public string FilePath { get; }
    public List<Type> ScriptTypes { get; }
    public DateTime LoadTime { get; }

    private bool _disposed = false;

    public LoadedLibrary(string name, Assembly assembly, ScriptLibraryLoadContext loadContext, 
                        string filePath, List<Type> scriptTypes)
    {
        Name = name;
        Assembly = assembly;
        LoadContext = loadContext;
        FilePath = filePath;
        ScriptTypes = scriptTypes;
        LoadTime = DateTime.Now;
    }

    public void Dispose()
    {
        if (_disposed) return;

        try
        {
            LoadContext?.Unload();
            _disposed = true;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error disposing script library '{Name}': {ex.Message}");
        }
    }
}
