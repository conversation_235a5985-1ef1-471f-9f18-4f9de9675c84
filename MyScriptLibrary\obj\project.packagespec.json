﻿"restore":{"projectUniqueName":"D:\\Projects\\TestLE\\MyScriptLibrary\\MyScriptLibrary.csproj","projectName":"MyScriptLibrary","projectPath":"D:\\Projects\\TestLE\\MyScriptLibrary\\MyScriptLibrary.csproj","outputPath":"D:\\Projects\\TestLE\\MyScriptLibrary\\obj\\","projectStyle":"PackageReference","fallbackFolders":["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net6.0"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net6.0":{"targetAlias":"net6.0","projectReferences":{}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"10.0.100"}"frameworks":{"net6.0":{"targetAlias":"net6.0","imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"downloadDependencies":[{"name":"Microsoft.AspNetCore.App.Ref","version":"[6.0.36, 6.0.36]"},{"name":"Microsoft.NETCore.App.Ref","version":"[6.0.36, 6.0.36]"},{"name":"Microsoft.WindowsDesktop.App.Ref","version":"[6.0.36, 6.0.36]"}],"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Users\\<USER>\\.dotnet\\sdk\\10.0.100-preview.7.25380.108\\RuntimeIdentifierGraph.json"}}