# Hot Reload Testing Guide

This guide will help you test the hot reloading functionality of the DLL-based script library system.

## 🔥 Hot Reload Improvements Made

The system has been enhanced to handle file locking issues that prevented hot reloading:

### 1. **Assembly Unloading Improvements**
- Added proper garbage collection triggers after unloading
- Improved reference cleanup in script manager
- Better error handling during unload process

### 2. **File Locking Solutions**
- Build process now uses temporary files to avoid locking
- Added retry logic with delays for file operations
- File watcher includes delay before triggering reload

### 3. **Enhanced Logging**
- Better visibility into the unload/reload process
- Clear status messages for debugging

## 🧪 Testing Hot Reload

### Step 1: Initial Setup
1. **Start the game** with TestLE mod loaded
2. **Open script management UI** 
3. **Load MyScriptLibrary** from the Libraries tab
4. **Start HelloWorldScript** and watch the console output

### Step 2: Test Hot Reload
1. **Keep the game running** with the script active
2. **Modify HelloWorldScript.cs** (change the log messages)
3. **Run `build.bat`** or `dotnet build`
4. **Watch the console** - you should see:
   - "Hot reloading library: MyScriptLibrary"
   - "Unloading library: MyScriptLibrary"
   - "Found X scripts to unload from library MyScriptLibrary"
   - "Successfully unloaded library: MyScriptLibrary"
   - "Loaded script library: MyScriptLibrary (X script types)"

### Step 3: Verify Changes
1. **Stop and restart the script** in the UI
2. **Check that your changes** are reflected in the console output
3. **The script should show your modified messages**

## 🔧 Troubleshooting

### If Hot Reload Fails:

1. **Check the build output** for file locking warnings
2. **Wait a few seconds** and try building again
3. **Manually reload** the library using the UI if needed
4. **Check console logs** for error messages

### Common Issues:

- **File still locked**: The retry mechanism should handle this, but you may need to wait longer
- **Scripts not updating**: Make sure to stop running scripts before hot reload
- **Build errors**: Check that all references are correct and TestLE is built

## 📝 Test Script Modifications

Try these modifications to test hot reloading:

### HelloWorldScript.cs
```csharp
// Change the log messages
Log("🎉 Hot reload is working! Version 2.0");

// Add new functionality
Log($"Current time: {DateTime.Now:HH:mm:ss}");

// Change timing
yield return new WaitForSeconds(0.5f); // Faster execution
```

### PlayerInfoScript.cs
```csharp
// Add new information
Log($"Scene load time: {Time.time:F1} seconds");
Log($"FPS: {1f / Time.deltaTime:F0}");
```

### SimpleGUIScript.cs
```csharp
// Change the window title
GUILayout.Window(54321, new Rect(200, 200, 350, 250), 
    (GUI.WindowFunction)DrawWindow, "Hot Reload Test Window v2.0");

// Add new GUI elements
if (GUILayout.Button("Hot Reload Test"))
{
    Log("Hot reload button clicked!");
}
```

## ✅ Success Indicators

Hot reloading is working correctly when:

1. **Build completes** without file locking errors
2. **Console shows** unload/reload messages
3. **Script changes** are visible immediately
4. **No game restart** required
5. **Performance** remains smooth

## 🚀 Advanced Testing

For more thorough testing:

1. **Test with multiple scripts** running simultaneously
2. **Try syntax errors** to ensure error handling works
3. **Test rapid rebuilds** to stress-test the system
4. **Monitor memory usage** to ensure no leaks

The hot reload system should now work reliably, allowing you to develop and test scripts without restarting the game!
