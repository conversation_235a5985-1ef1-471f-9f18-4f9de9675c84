﻿using MelonLoader;
using TestLE.Routine;
using TestLE.Routine.CombatRoutines;
using TestLE.Scripting;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE;

public class Test : MelonMod
{
    private MelonPreferences_Category CombatCategory = null!;
    private MelonPreferences_Entry<int> CombatRoutineEntry = null!;

    public readonly List<CombatRoutine> CombatRoutines = new()
    {
        new Beastmaster_Kripp(),
        new Necromancer_Mattjestic()
    };

    private readonly List<Patch> PatchInstances = new();
    private readonly CSharpScriptUI _csharpScriptUI = new();
    private bool _showScriptUI = true;

    public override void OnInitializeMelon()
    {
        CombatCategory = MelonPreferences.CreateCategory("Combat");
        CombatRoutineEntry = CombatCategory.CreateEntry("CombatRoutineEntry", 0);

        if (CombatRoutineEntry.Value >= 0 && CombatRoutineEntry.Value < CombatRoutines.Count)
            CURRENT_ROUTINE = CombatRoutines[CombatRoutineEntry.Value];
        else
            CURRENT_ROUTINE = CombatRoutines.FirstOrDefault();

        // Reflection: Find all non-abstract subclasses of TestLE.Patch, instantiate, add to list, call Setup
        try
        {
            var patchType = typeof(Patch);
            var assembly = patchType.Assembly;
            var patchTypes = assembly.GetTypes()
                .Where(t => t.IsSubclassOf(patchType) && !t.IsAbstract && t.GetConstructor(Type.EmptyTypes) != null)
                .ToList();

            foreach (var type in patchTypes)
            {
                try
                {
                    var instance = (Patch)Activator.CreateInstance(type)!;
                    PatchInstances.Add(instance);
                    try
                    {
                        instance.Setup();
                    }
                    catch (Exception ex)
                    {
                        MelonLogger.Error($"Error calling Setup on {type.FullName}: {ex}");
                    }
                }
                catch (Exception ex)
                {
                    MelonLogger.Error($"Error instantiating {type.FullName}: {ex}");
                }
            }
            MelonLogger.Msg($"Loaded {PatchInstances.Count} Patch subclasses.");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Reflection error loading Patch subclasses: {ex}");
        }

        // Initialize C# scripting system
        CSharpIntegration.Initialize();
    }

    public override async void OnSceneWasInitialized(int buildIndex, string sceneName)
    {
        MelonLogger.Msg($"Scene initialized: {sceneName}");

        // PLAYER = null!;
        CURRENT_SCENE = sceneName;
        ResetGlobals();
    }

    public override void OnUpdate()
    {
        if (Input.GetKeyDown(KeyCode.PageDown))
        {
            // Switch to the next combat routine
            CURRENT_ROUTINE = CURRENT_ROUTINE != null ? CombatRoutines[(CombatRoutines.IndexOf(CURRENT_ROUTINE) + 1) % CombatRoutines.Count] : CombatRoutines.FirstOrDefault();
            CombatRoutineEntry.Value = CombatRoutines.IndexOf(CURRENT_ROUTINE!);
            MelonPreferences.Save();
        }

        if (Input.GetKeyDown(KeyCode.PageUp))
        {
            // Switch to the previous combat routine
            CURRENT_ROUTINE = CURRENT_ROUTINE != null ? CombatRoutines[(CombatRoutines.IndexOf(CURRENT_ROUTINE) - 1 + CombatRoutines.Count) % CombatRoutines.Count] : CombatRoutines.LastOrDefault();
            CombatRoutineEntry.Value = CombatRoutines.IndexOf(CURRENT_ROUTINE!);
            MelonPreferences.Save();
        }

        if (Input.GetKeyDown(KeyCode.F8))
            SHOW_UI = !SHOW_UI;

        if (Input.GetKeyDown(KeyCode.F9))
            _showScriptUI = !_showScriptUI;

        if (Input.GetKeyDown(KeyCode.F10))
            CSharpIntegration.ReloadAllScripts();

        if (PLAYER == null)
            return;

        if (CURRENT_ROUTINE is { PotionHealthUse: > 0f } && PLAYER.playerHealth.currentHealth / PLAYER.playerHealth.maxHealth <= CURRENT_ROUTINE.PotionHealthUse) // TODO: Move to routine
            PlayerHelpers.UsePotion();

        if (Input.GetKeyDown(KeyCode.Mouse4))
        {
            MelonLogger.Msg("Mouse 4 pressed.");
            MelonCoroutines.Start(Input.GetKey(KeyCode.LeftShift) ? AUCTION_HOUSE_UI.ListHoveredItem() : AUCTION_HOUSE_UI.SearchHoveredItem());
        }

        // Update all running C# scripts
        CSharpIntegration.UpdateScripts();
    }

    public override void OnGUI()
    {
        if (PLAYER == null || !SHOW_UI)
            return;

        if (MAIN_ROUTINE_COROUTINE != null)
        {
            if (GUI.Button(new Rect(10, 10, 50, 20), "Stop"))
            {
                MelonCoroutines.Stop(MAIN_ROUTINE_COROUTINE);
                MAIN_ROUTINE_COROUTINE = null;
            }
        }
        else
        {
            if (GUI.Button(new Rect(10, 10, 50, 20), "Start"))
            {
                MAIN_ROUTINE = new MainRoutine();
                MAIN_ROUTINE_COROUTINE = MelonCoroutines.Start(MAIN_ROUTINE);
            }
        }

        GUI.Label(new Rect(10, 30, 200, 20), $"Routine: {(CURRENT_ROUTINE != null ? CURRENT_ROUTINE.GetType().Name : "None")}");

        // C# Script UI
        if (_showScriptUI)
        {
            GUI.BeginGroup(new Rect(10, 50, 300, 150));
            _csharpScriptUI.DrawUI();
            GUI.EndGroup();
        }

        // Render GUI for all running C# scripts
        CSharpIntegration.RenderScriptGUI();

        // Auction House UI
        var auctionUITop = _showScriptUI ? 210 : 50;
        GUI.BeginGroup(new Rect(10, auctionUITop, Screen.width - 20, Screen.height - auctionUITop - 10));
        AUCTION_HOUSE_UI.DrawUI();
        GUI.EndGroup();
    }
}
