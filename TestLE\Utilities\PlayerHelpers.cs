﻿using System.Collections;
using Il2Cpp;
using MelonLoader;
using UnityEngine;
using UnityEngine.AI;

namespace TestLE.Utilities;

public static class PlayerHelpers
{
    private static DateTime _lastMovementAbilityAttempt = DateTime.MinValue;
    private static Vector3 _lastMovementAbilityTarget = Vector3.zero;
    private const float MOVEMENT_ABILITY_RETRY_DELAY = 2f; // Seconds between retries to same position
    public static bool IsAbilityOnCooldown(int index)
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return false;
        }

        if (PLAYER.usingAbilityState == null)
        {
            MelonLogger.Msg("usingAbilityState is null");
            return false;
        }

        return PLAYER.usingAbilityState.OnCooldown(BufferableAbilityType.Bar, index);
    }
    
    public static string? GetAbilityName(int index)
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return null;
        }

        if (PLAYER.usingAbilityState != null)
            return PLAYER.usingAbilityState.abilityList.getAbility(index).abilityName;
        
        MelonLogger.Msg("usingAbilityState is null");
        return null;

    }

    public static void UseAbility(int index, Transform targetTransform)
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return;
        }

        if (PLAYER.usingAbilityState == null)
        {
            MelonLogger.Msg("usingAbilityState is null");
            return;
        }

        PLAYER.usingAbilityState.UseBufferableAbilityCommand(BufferableAbilityType.Bar, index, targetTransform.position, targetTransform, true, true, true, true);
    }

    public static void UseMovementAbility(Transform targetTransform)
    {
        if (CURRENT_ROUTINE == null || CURRENT_ROUTINE.MovementSkillIndex == -1 || IsAbilityOnCooldown(CURRENT_ROUTINE.MovementSkillIndex))
            return;

        UseAbility(CURRENT_ROUTINE.MovementSkillIndex, targetTransform);
    }

    public static void UseMovementAbility(Vector3 position)
    {
        if (CURRENT_ROUTINE == null || CURRENT_ROUTINE.MovementSkillIndex == -1 || IsAbilityOnCooldown(CURRENT_ROUTINE.MovementSkillIndex))
            return;

        // Prevent spamming movement ability to the same position
        var timeSinceLastAttempt = (DateTime.Now - _lastMovementAbilityAttempt).TotalSeconds;
        var distanceFromLastTarget = Vector3.Distance(position, _lastMovementAbilityTarget);

        if (timeSinceLastAttempt < MOVEMENT_ABILITY_RETRY_DELAY && distanceFromLastTarget < 3f)
        {
            MelonLogger.Msg($"Movement ability blocked - too soon to retry same position (last attempt: {timeSinceLastAttempt:F1}s ago)");
            return;
        }

        _lastMovementAbilityAttempt = DateTime.Now;
        _lastMovementAbilityTarget = position;

        PLAYER.usingAbilityState.UseBufferableAbilityCommand(BufferableAbilityType.Bar, CURRENT_ROUTINE.MovementSkillIndex, position, null, true, true, true, true);
    }

    public static void UsePortal()
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return;
        }

        if (PLAYER.usingAbilityState == null)
        {
            MelonLogger.Msg("usingAbilityState is null");
            return;
        }

        PLAYER.usingAbilityState.UsePortalCommand(Vector3.zero, true);
    }

    public static bool UsePotion()
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return false;
        }

        if (PLAYER.healthPotion.currentCharges <= 0)
        {
            MelonLogger.Msg("No potions available");
            return false;
        }

        PLAYER.PotionKeyPressed();
        return true;
    }

    /// <summary>
    /// Will try once to move to the position.
    /// </summary>
    /// <param name="position">Position to move to</param>
    public static void MoveTo(Vector3 position)
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return;
        }

        Move(position);
    }

    /// <summary>
    /// Will force the player to move to the position, looping until the player is within the stoppingDistance or maxTries is reached.
    /// </summary>
    /// <param name="position">Position to move to</param>
    /// <param name="stoppingDistance">Distance to stop moving</param>
    /// <param name="maxTries">Max number of tries</param>
    /// <param name="delayBetweentries">Delay between tries</param>
    /// <returns>IEnumerator for coroutine</returns>
    public static IEnumerator MoveToForce(Vector3 position, float stoppingDistance = 1f, int maxTries = 15, float delayBetweentries = 0.3333f)
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            yield break;
        }

        for (var i = 0; i < maxTries; i++)
        {
            if (Vector3.Distance(PLAYER.transform.position, position) <= stoppingDistance)
                break;

            Move(position);
            yield return new WaitForSeconds(delayBetweentries);
        }
    }

    private static void Move(Vector3 position)
    {
        if (!NavMesh.SamplePosition(position, out var hit, 10f, -1))
        {
            MelonLogger.Msg("Failed to find a valid move position");
            return;
        }

        var path = new NavMeshPath();
        PLAYER.navMeshAgent.CalculatePath(hit.position, path);
        if (path.corners.Length == 0)
        {
            MelonLogger.Msg("Failed to find a valid path, defaulting to full path move");
            InternalMove(hit.position);
            return;
        }

        // Find the best corner to move to
        Vector3? bestCorner = null;
        bool useMovementAbility = false;

        foreach (var corner in path.corners)
        {
            var distance = Vector3.Distance(PLAYER.transform.position, corner);

            // Skip corners that are too close (we're already there)
            if (distance < 3f)
                continue;

            // Prefer movement ability for medium distances if available and not recently attempted
            if (distance is >= 8f and <= 20f &&
                CURRENT_ROUTINE?.MovementSkillIndex != -1 &&
                CURRENT_ROUTINE != null &&
                !IsAbilityOnCooldown(CURRENT_ROUTINE.MovementSkillIndex))
            {
                // Additional check: don't use movement ability if we recently tried the same area
                var timeSinceLastAttempt = (DateTime.Now - _lastMovementAbilityAttempt).TotalSeconds;
                var distanceFromLastTarget = Vector3.Distance(corner, _lastMovementAbilityTarget);

                if (timeSinceLastAttempt >= MOVEMENT_ABILITY_RETRY_DELAY || distanceFromLastTarget >= 5f)
                {
                    // Check if we can actually reach this position with movement ability
                    if (NavMesh.SamplePosition(corner, out var abilityHit, 5f, -1))
                    {
                        bestCorner = abilityHit.position;
                        useMovementAbility = true;
                        break; // Use the first suitable corner for movement ability
                    }
                }
            }

            // For shorter distances or when movement ability isn't available, use regular movement
            if (distance >= 3f && bestCorner == null)
            {
                bestCorner = corner;
                useMovementAbility = false;
                // Don't break here, continue looking for movement ability opportunities
            }
        }

        // Execute the chosen movement
        if (bestCorner.HasValue)
        {
            if (useMovementAbility)
            {
                UseMovementAbility(bestCorner.Value);
                // Note: We don't fallback to InternalMove here because UseMovementAbility
                // now has its own spam protection. If it fails, the next Move() call will
                // try regular movement or wait for the retry delay.
            }
            else
            {
                InternalMove(bestCorner.Value);
            }
        }
        else
        {
            // If no suitable corner found, move directly to destination
            InternalMove(hit.position);
        }
    }

    private static void InternalMove(Vector3 position)
    {
        // PLAYER!.movingState.MouseClickMoveCommand(CAMERA.ScreenPointToRay(CAMERA.WorldToScreenPoint(position)), false, 1f, true, position, true);
        PLAYER!.movingState.MoveToPointNoChecks(position, true);
    }

    // public static void StoreMaterials()
    // {
    //     if (STORE_MATERIALS_BUTTON == null)
    //     {
    //         MelonLogger.Msg("STORE_MATERIALS_BUTTON is null");
    //         return;
    //     }
    //
    //     STORE_MATERIALS_BUTTON.onClick.Invoke();
    // }
    public static Vector3? GetPlayerPosition()
    {
        if (PLAYER != null)
            return PLAYER.transform.position;
        
        MelonLogger.Msg("GetPlayerPosition: PLAYER is null");
        return null;

    }

    public static float? GetPlayerHealth()
    {
        if (PLAYER != null)
            return PLAYER.playerHealth.currentHealth;
        
        MelonLogger.Msg("GetPlayerHealth: PLAYER is null");
        return null;
    }

    public static bool IsPlayerAlive()
    {
        if (PLAYER != null)
            return PLAYER.playerHealth.currentHealth > 0;
        
        MelonLogger.Msg("IsPlayerAlive: PLAYER is null");
        return false;
    }
}
