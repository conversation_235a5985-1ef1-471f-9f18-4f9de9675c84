# My Script Library

A simple C# script library for the TestLE mod that demonstrates various scripting capabilities.

## Scripts Included

### 1. HelloWorldScript
- **Purpose**: Basic "Hello World" example
- **Features**: Simple coroutine execution with logging
- **Duration**: ~3 seconds
- **Usage**: Great for testing that the scripting system is working

### 2. PlayerInfoScript
- **Purpose**: Demonstrates game API usage and update loops
- **Features**: 
  - Displays player information
  - Counts enemies, ground items, and interactables
  - Implements `IUpdatableScript` for frame-by-frame updates
- **Duration**: 10 seconds with periodic updates
- **Usage**: Shows how to access game objects and data

### 3. SimpleGUIScript
- **Purpose**: Demonstrates GUI creation and user interaction
- **Features**:
  - Creates a draggable window
  - Text input field
  - Interactive buttons
  - Keyboard shortcuts (F2 to toggle)
- **Duration**: Runs indefinitely until stopped
- **Usage**: Example of creating user interfaces

## How to Use

1. **Build the Project**:
   ```bash
   dotnet build
   ```
   Or simply double-click `build.bat` for a convenient build script.
   The DLL will automatically be copied to the TestLE ScriptLibraries folder.

2. **Load in Game**:
   - Start Last Epoch with TestLE mod
   - Open the script management UI (usually accessible through the mod's interface)
   - Go to the "Libraries" tab
   - You should see "MyScriptLibrary" listed
   - Individual scripts will appear in the "Scripts" tab as:
     - `MyScriptLibrary.HelloWorldScript`
     - `MyScriptLibrary.PlayerInfoScript`
     - `MyScriptLibrary.SimpleGUIScript`

3. **Run Scripts**:
   - Select a script and click "Start"
   - Watch the console for log messages
   - For the GUI script, press F2 to open the window

## Quick Start

1. **Clone or copy this folder** to your development location
2. **Double-click `build.bat`** to build the library
3. **Start the game** with TestLE mod loaded
4. **Open script management UI** and load the library
5. **Start any script** to see it in action!

## Customization

Feel free to modify these scripts or create new ones:

- **Change the namespace** from `MyScriptLibrary` to your preferred name
- **Add new scripts** by creating classes that inherit from `ScriptBase`
- **Implement interfaces** like `IUpdatableScript` or `IGuiScript` for additional functionality
- **Access game APIs** through the properties provided by `ScriptBase`

## Available APIs

Scripts have access to:
- `Player` - Current player object
- `Enemies` - List of enemies in the scene
- `GroundItems` - List of items on the ground
- `Interactables` - List of interactable objects
- `CurrentScene` - Name of the current scene
- `Log()`, `LogWarning()`, `LogError()` - Logging methods

## Hot Reloading

The library supports hot reloading:
1. Make changes to your scripts
2. Build the project (`dotnet build`)
3. TestLE will automatically detect the change and reload the library
4. All scripts will be updated with your changes

## Tips

- Use coroutines (`yield return`) for time-based operations
- Check `CanExecute()` to ensure scripts only run when appropriate
- Handle exceptions to prevent crashes
- Use meaningful names for your scripts
- Test frequently with hot reloading during development
