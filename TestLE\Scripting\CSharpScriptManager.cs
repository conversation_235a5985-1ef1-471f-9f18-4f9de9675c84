using MelonLoader;
using MelonLoader.Utils;

namespace TestLE.Scripting;

/// <summary>
/// C# script manager with hot reloading support for DLL-based script libraries.
/// Manages the lifecycle of script libraries including loading, unloading, and execution.
/// </summary>
public class CSharpScriptManager
{
    private static CSharpScriptManager? _instance;
    public static CSharpScriptManager Instance => _instance ??= new CSharpScriptManager();

    private readonly Dictionary<string, IScriptRoutine> _loadedScripts = new();
    private readonly Dictionary<string, IUpdatableScript> _updatableScripts = new();
    private readonly Dictionary<string, IGuiScript> _guiScripts = new();
    private readonly HashSet<string> _runningScripts = new();
    private readonly string _librariesDirectory;
    private ScriptLibraryWatcher? _libraryWatcher;
    private ScriptLibraryLoader? _libraryLoader;

    public bool IsInitialized { get; private set; }
    public IReadOnlyDictionary<string, IScriptRoutine> LoadedScripts => _loadedScripts;
    public IReadOnlySet<string> RunningScripts => _runningScripts;

    /// <summary>
    /// Get list of available script libraries (without .dll extension)
    /// </summary>
    public List<string> GetAvailableLibraries()
    {
        return _libraryWatcher?.GetAvailableLibraries() ?? new List<string>();
    }

    /// <summary>
    /// Get list of all available script names from loaded libraries
    /// </summary>
    public List<string> GetAvailableScripts()
    {
        return _loadedScripts.Keys.ToList();
    }

    private CSharpScriptManager()
    {
        _librariesDirectory = Path.Combine(MelonEnvironment.ModsDirectory, "TestLE", "ScriptLibraries");
    }

    public void Initialize()
    {
        if (IsInitialized) return;

        try
        {
            Directory.CreateDirectory(_librariesDirectory);

            // Initialize library loader
            _libraryLoader = new ScriptLibraryLoader();
            _libraryLoader.LibraryLoaded += OnLibraryLoaded;
            _libraryLoader.LibraryUnloaded += OnLibraryUnloaded;
            _libraryLoader.LibraryLoadFailed += OnLibraryLoadFailed;

            // Setup library watcher
            SetupLibraryWatcher();

            // Load all existing libraries
            LoadAllLibraries();

            IsInitialized = true;
            MelonLogger.Msg($"CSharpScriptManager initialized with {_loadedScripts.Count} scripts from libraries");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to initialize CSharpScriptManager: {ex.Message}");
        }
    }

    public void Shutdown()
    {
        _libraryWatcher?.Dispose();
        _libraryLoader?.Dispose();
        _runningScripts.Clear();
        
        // Unload all scripts
        foreach (var script in _loadedScripts.Values)
        {
            try
            {
                script.OnUnload();
            }
            catch (Exception ex)
            {
                MelonLogger.Error($"Error unloading script '{script.Name}': {ex.Message}");
            }
        }
        
        _loadedScripts.Clear();
        _updatableScripts.Clear();
        _guiScripts.Clear();
        
        IsInitialized = false;
    }

    /// <summary>
    /// Load a script library from file.
    /// </summary>
    public bool LoadLibrary(string libraryName)
    {
        try
        {
            var libraryPath = _libraryWatcher?.GetLibraryPath(libraryName);
            if (string.IsNullOrEmpty(libraryPath) || !File.Exists(libraryPath))
            {
                MelonLogger.Error($"Could not find library file: {libraryName}");
                return false;
            }

            // Unload existing library if it exists
            UnloadLibrary(libraryName);

            // Load the library
            return _libraryLoader?.LoadLibrary(libraryName, libraryPath) ?? false;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error loading library '{libraryName}': {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Unload a script library and all its scripts.
    /// </summary>
    public bool UnloadLibrary(string libraryName)
    {
        try
        {
            MelonLogger.Msg($"Unloading library: {libraryName}");

            // Find and unload all scripts from this library
            var scriptsToRemove = new List<string>();
            foreach (var kvp in _loadedScripts)
            {
                var scriptType = kvp.Value.GetType();
                if (scriptType.Assembly.GetName().Name == libraryName)
                {
                    scriptsToRemove.Add(kvp.Key);
                }
            }

            MelonLogger.Msg($"Found {scriptsToRemove.Count} scripts to unload from library {libraryName}");

            // Stop and unload all scripts from this library
            foreach (var scriptName in scriptsToRemove)
            {
                UnloadScript(scriptName);
            }

            // Clear any remaining references
            scriptsToRemove.Clear();

            // Unload the library
            var result = _libraryLoader?.UnloadLibrary(libraryName) ?? false;

            if (result)
            {
                MelonLogger.Msg($"Successfully unloaded library: {libraryName}");
            }

            return result;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error unloading library '{libraryName}': {ex.Message}");
            return false;
        }
    }



    /// <summary>
    /// Unload a script and clean up its resources.
    /// </summary>
    public bool UnloadScript(string scriptName)
    {
        try
        {
            // Stop the script if it's running
            StopScript(scriptName);

            // Call OnUnload if script exists
            if (_loadedScripts.TryGetValue(scriptName, out var script))
            {
                try
                {
                    script.OnUnload();
                }
                catch (Exception ex)
                {
                    MelonLogger.Warning($"Error calling OnUnload for script '{scriptName}': {ex.Message}");
                }
            }

            // Remove from all collections
            _loadedScripts.Remove(scriptName);
            _updatableScripts.Remove(scriptName);
            _guiScripts.Remove(scriptName);

            // Clear the script reference
            script = null;

            MelonLogger.Msg($"Unloaded script: {scriptName}");
            return true;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error unloading script '{scriptName}': {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Start executing a script.
    /// </summary>
    public bool StartScript(string scriptName)
    {
        if (_runningScripts.Contains(scriptName))
        {
            MelonLogger.Warning($"Script '{scriptName}' is already running");
            return false;
        }

        if (!_loadedScripts.ContainsKey(scriptName))
        {
            MelonLogger.Error($"Script '{scriptName}' is not loaded");
            return false;
        }

        _runningScripts.Add(scriptName);
        MelonLogger.Msg($"Started script: {scriptName}");
        return true;
    }

    /// <summary>
    /// Stop executing a script.
    /// </summary>
    public bool StopScript(string scriptName)
    {
        if (_runningScripts.Remove(scriptName))
        {
            MelonLogger.Msg($"Stopped script: {scriptName}");
            return true;
        }
        return false;
    }

    /// <summary>
    /// Check if a script is currently running.
    /// </summary>
    public bool IsScriptRunning(string scriptName)
    {
        return _runningScripts.Contains(scriptName);
    }

    /// <summary>
    /// Get a loaded script by name.
    /// </summary>
    public IScriptRoutine? GetScript(string scriptName)
    {
        return _loadedScripts.TryGetValue(scriptName, out var script) ? script : null;
    }

    /// <summary>
    /// Reload all script libraries from disk.
    /// </summary>
    public void ReloadAllScripts()
    {
        MelonLogger.Msg("Reloading all script libraries...");

        var libraryNames = _libraryLoader?.LoadedLibraries.Keys.ToList() ?? new List<string>();
        foreach (var libraryName in libraryNames)
        {
            LoadLibrary(libraryName);
        }

        // Also load any new libraries
        LoadAllLibraries();
    }

    /// <summary>
    /// Call OnUpdate for all running updatable scripts.
    /// </summary>
    public void UpdateScripts()
    {
        foreach (var kvp in _updatableScripts)
        {
            if (_runningScripts.Contains(kvp.Key))
            {
                try
                {
                    kvp.Value.OnUpdate();
                }
                catch (Exception ex)
                {
                    MelonLogger.Error($"Error in script '{kvp.Key}' OnUpdate: {ex.Message}");
                }
            }
        }
    }

    /// <summary>
    /// Call OnGUI for all running GUI scripts.
    /// </summary>
    public void RenderScriptGUI()
    {
        foreach (var kvp in _guiScripts)
        {
            if (_runningScripts.Contains(kvp.Key))
            {
                try
                {
                    kvp.Value.OnGUI();
                }
                catch (Exception ex)
                {
                    MelonLogger.Error($"Error in script '{kvp.Key}' OnGUI: {ex.Message}");
                }
            }
        }
    }

    private void SetupLibraryWatcher()
    {
        _libraryWatcher = new ScriptLibraryWatcher(_librariesDirectory);

        _libraryWatcher.LibraryChanged += OnLibraryChanged;
        _libraryWatcher.LibraryCreated += OnLibraryCreated;
        _libraryWatcher.LibraryDeleted += OnLibraryDeleted;
        _libraryWatcher.LibraryRenamed += OnLibraryRenamed;

        _libraryWatcher.StartWatching();
    }

    private void LoadAllLibraries()
    {
        var libraryFiles = _libraryWatcher?.GetAvailableLibraries() ?? new List<string>();
        foreach (var libraryName in libraryFiles)
        {
            LoadLibrary(libraryName);
        }
    }

    private void OnLibraryChanged(string libraryName)
    {
        MelonLogger.Msg($"Hot reloading library: {libraryName}");
        LoadLibrary(libraryName);
    }

    private void OnLibraryCreated(string libraryName)
    {
        MelonLogger.Msg($"New library detected: {libraryName}");
        LoadLibrary(libraryName);
    }

    private void OnLibraryDeleted(string libraryName)
    {
        MelonLogger.Msg($"Library deleted: {libraryName}");
        UnloadLibrary(libraryName);
    }

    private void OnLibraryRenamed(string oldName, string newName)
    {
        MelonLogger.Msg($"Library renamed: {oldName} -> {newName}");
        UnloadLibrary(oldName);
        LoadLibrary(newName);
    }

    private void OnLibraryLoaded(string libraryName, LoadedLibrary library)
    {
        // Create instances of all script types in the library
        foreach (var scriptType in library.ScriptTypes)
        {
            try
            {
                var scriptInstance = _libraryLoader?.CreateScriptInstance<IScriptRoutine>(scriptType);
                if (scriptInstance == null) continue;

                var scriptName = $"{libraryName}.{scriptType.Name}";

                // Register the script
                _loadedScripts[scriptName] = scriptInstance;

                // Register for additional interfaces
                if (scriptInstance is IUpdatableScript updatable)
                    _updatableScripts[scriptName] = updatable;

                if (scriptInstance is IGuiScript guiScript)
                    _guiScripts[scriptName] = guiScript;

                // Call OnLoad
                scriptInstance.OnLoad();

                MelonLogger.Msg($"Loaded script: {scriptName}");
            }
            catch (Exception ex)
            {
                MelonLogger.Error($"Error creating instance of script type '{scriptType.Name}': {ex.Message}");
            }
        }
    }

    private void OnLibraryUnloaded(string libraryName)
    {
        MelonLogger.Msg($"Library unloaded: {libraryName}");
    }

    private void OnLibraryLoadFailed(string libraryName, Exception exception)
    {
        MelonLogger.Error($"Failed to load library '{libraryName}': {exception.Message}");
    }
}
