using MelonLoader;
using MelonLoader.Utils;

namespace TestLE.Scripting;

/// <summary>
/// C# script manager with hot reloading support.
/// Manages the lifecycle of C# scripts including compilation, loading, and execution.
/// </summary>
public class CSharpScriptManager
{
    private static CSharpScriptManager? _instance;
    public static CSharpScriptManager Instance => _instance ??= new CSharpScriptManager();

    private readonly Dictionary<string, IScriptRoutine> _loadedScripts = new();
    private readonly Dictionary<string, IUpdatableScript> _updatableScripts = new();
    private readonly Dictionary<string, IGuiScript> _guiScripts = new();
    private readonly HashSet<string> _runningScripts = new();
    private readonly string _scriptsDirectory;
    private ScriptFileWatcher? _fileWatcher;

    public bool IsInitialized { get; private set; }
    public IReadOnlyDictionary<string, IScriptRoutine> LoadedScripts => _loadedScripts;
    public IReadOnlySet<string> RunningScripts => _runningScripts;

    /// <summary>
    /// Get list of available script names (without .cs extension)
    /// </summary>
    public List<string> GetAvailableScripts()
    {
        return _fileWatcher?.GetAvailableScripts() ?? new List<string>();
    }

    private CSharpScriptManager()
    {
        _scriptsDirectory = Path.Combine(MelonEnvironment.ModsDirectory, "TestLE", "CSharpScripts");
    }

    public void Initialize()
    {
        if (IsInitialized) return;

        try
        {
            Directory.CreateDirectory(_scriptsDirectory);

            // Initialize the compiler
            CSharpCompiler.Instance.Initialize();

            // Setup file watcher
            SetupFileWatcher();

            // Load all existing scripts
            LoadAllScripts();

            IsInitialized = true;
            MelonLogger.Msg($"CSharpScriptManager initialized with {_loadedScripts.Count} scripts");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to initialize CSharpScriptManager: {ex.Message}");
        }
    }

    public void Shutdown()
    {
        _fileWatcher?.Dispose();
        _runningScripts.Clear();
        
        // Unload all scripts
        foreach (var script in _loadedScripts.Values)
        {
            try
            {
                script.OnUnload();
            }
            catch (Exception ex)
            {
                MelonLogger.Error($"Error unloading script '{script.Name}': {ex.Message}");
            }
        }
        
        _loadedScripts.Clear();
        _updatableScripts.Clear();
        _guiScripts.Clear();
        
        CSharpCompiler.Instance.Shutdown();
        IsInitialized = false;
    }

    /// <summary>
    /// Load and compile a script from file.
    /// </summary>
    public bool LoadScript(string scriptName)
    {
        try
        {
            var sourceCode = _fileWatcher?.ReadScriptContent(scriptName);
            if (string.IsNullOrEmpty(sourceCode))
            {
                MelonLogger.Error($"Could not read script file: {scriptName}");
                return false;
            }

            // Unload existing script if it exists
            UnloadScript(scriptName);

            // Compile the script
            var result = CSharpCompiler.Instance.CompileScript(scriptName, sourceCode);
            if (!result.Success)
            {
                MelonLogger.Error($"Failed to compile script '{scriptName}':");
                foreach (var error in result.Errors)
                {
                    MelonLogger.Error($"  {error}");
                }
                return false;
            }

            // Create script instance
            var scriptInstance = result.Script?.CreateInstance<IScriptRoutine>();
            if (scriptInstance == null)
            {
                MelonLogger.Error($"Script '{scriptName}' does not implement IScriptRoutine or could not be instantiated");
                return false;
            }

            // Register the script
            _loadedScripts[scriptName] = scriptInstance;

            // Register for additional interfaces
            if (scriptInstance is IUpdatableScript updatable)
                _updatableScripts[scriptName] = updatable;
            
            if (scriptInstance is IGuiScript guiScript)
                _guiScripts[scriptName] = guiScript;

            // Call OnLoad
            scriptInstance.OnLoad();

            MelonLogger.Msg($"Loaded script: {scriptName}");
            return true;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error loading script '{scriptName}': {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Unload a script and clean up its resources.
    /// </summary>
    public bool UnloadScript(string scriptName)
    {
        try
        {
            // Stop the script if it's running
            StopScript(scriptName);

            // Call OnUnload if script exists
            if (_loadedScripts.TryGetValue(scriptName, out var script))
            {
                script.OnUnload();
            }

            // Remove from all collections
            _loadedScripts.Remove(scriptName);
            _updatableScripts.Remove(scriptName);
            _guiScripts.Remove(scriptName);

            // Remove compiled script
            CSharpCompiler.Instance.RemoveScript(scriptName);

            MelonLogger.Msg($"Unloaded script: {scriptName}");
            return true;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error unloading script '{scriptName}': {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Start executing a script.
    /// </summary>
    public bool StartScript(string scriptName)
    {
        if (_runningScripts.Contains(scriptName))
        {
            MelonLogger.Warning($"Script '{scriptName}' is already running");
            return false;
        }

        if (!_loadedScripts.ContainsKey(scriptName))
        {
            MelonLogger.Error($"Script '{scriptName}' is not loaded");
            return false;
        }

        _runningScripts.Add(scriptName);
        MelonLogger.Msg($"Started script: {scriptName}");
        return true;
    }

    /// <summary>
    /// Stop executing a script.
    /// </summary>
    public bool StopScript(string scriptName)
    {
        if (_runningScripts.Remove(scriptName))
        {
            MelonLogger.Msg($"Stopped script: {scriptName}");
            return true;
        }
        return false;
    }

    /// <summary>
    /// Check if a script is currently running.
    /// </summary>
    public bool IsScriptRunning(string scriptName)
    {
        return _runningScripts.Contains(scriptName);
    }

    /// <summary>
    /// Get a loaded script by name.
    /// </summary>
    public IScriptRoutine? GetScript(string scriptName)
    {
        return _loadedScripts.TryGetValue(scriptName, out var script) ? script : null;
    }

    /// <summary>
    /// Reload all scripts from disk.
    /// </summary>
    public void ReloadAllScripts()
    {
        MelonLogger.Msg("Reloading all C# scripts...");
        
        var scriptNames = _loadedScripts.Keys.ToList();
        foreach (var scriptName in scriptNames)
        {
            LoadScript(scriptName);
        }
        
        // Also load any new scripts
        LoadAllScripts();
    }

    /// <summary>
    /// Call OnUpdate for all running updatable scripts.
    /// </summary>
    public void UpdateScripts()
    {
        foreach (var kvp in _updatableScripts)
        {
            if (_runningScripts.Contains(kvp.Key))
            {
                try
                {
                    kvp.Value.OnUpdate();
                }
                catch (Exception ex)
                {
                    MelonLogger.Error($"Error in script '{kvp.Key}' OnUpdate: {ex.Message}");
                }
            }
        }
    }

    /// <summary>
    /// Call OnGUI for all running GUI scripts.
    /// </summary>
    public void RenderScriptGUI()
    {
        foreach (var kvp in _guiScripts)
        {
            if (_runningScripts.Contains(kvp.Key))
            {
                try
                {
                    kvp.Value.OnGUI();
                }
                catch (Exception ex)
                {
                    MelonLogger.Error($"Error in script '{kvp.Key}' OnGUI: {ex.Message}");
                }
            }
        }
    }

    private void SetupFileWatcher()
    {
        _fileWatcher = new ScriptFileWatcher(_scriptsDirectory);
        
        _fileWatcher.ScriptChanged += OnScriptChanged;
        _fileWatcher.ScriptCreated += OnScriptCreated;
        _fileWatcher.ScriptDeleted += OnScriptDeleted;
        _fileWatcher.ScriptRenamed += OnScriptRenamed;
        
        _fileWatcher.StartWatching();
    }

    private void LoadAllScripts()
    {
        var scriptFiles = _fileWatcher?.GetAvailableScripts() ?? new List<string>();
        foreach (var scriptName in scriptFiles)
        {
            LoadScript(scriptName);
        }
    }

    private void OnScriptChanged(string scriptName)
    {
        MelonLogger.Msg($"Hot reloading script: {scriptName}");
        LoadScript(scriptName);
    }

    private void OnScriptCreated(string scriptName)
    {
        MelonLogger.Msg($"New script detected: {scriptName}");
        LoadScript(scriptName);
    }

    private void OnScriptDeleted(string scriptName)
    {
        MelonLogger.Msg($"Script deleted: {scriptName}");
        UnloadScript(scriptName);
    }

    private void OnScriptRenamed(string oldName, string newName)
    {
        MelonLogger.Msg($"Script renamed: {oldName} -> {newName}");
        UnloadScript(oldName);
        LoadScript(newName);
    }
}
