<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <OutputType>Library</OutputType>
    <AssemblyName>MyScriptLibrary</AssemblyName>
    <RootNamespace>MyScriptLibrary</RootNamespace>
    
    <!-- Enable hot reload support -->
    <UseSharedCompilation>false</UseSharedCompilation>
    <BuildInParallel>false</BuildInParallel>
    
    <!-- Copy to TestLE ScriptLibraries folder on build -->
    <TestLEModsPath Condition="'$(TestLEModsPath)' == ''">H:\SteamLibrary\steamapps\common\Last Epoch - Copy\Mods</TestLEModsPath>
    <ScriptLibrariesPath>$(TestLEModsPath)\TestLE\ScriptLibraries</ScriptLibrariesPath>
  </PropertyGroup>

  <ItemGroup>
    <!-- Reference TestLE main assembly for script interfaces -->
    <Reference Include="TestLE">
      <HintPath>$(TestLEModsPath)\TestLE.dll</HintPath>
      <Private>false</Private>
    </Reference>
    
    <!-- MelonLoader references -->
    <Reference Include="MelonLoader">
      <HintPath>$(TestLEModsPath)\..\MelonLoader\net6\MelonLoader.dll</HintPath>
      <Private>false</Private>
    </Reference>
    
    <!-- Unity references (add as needed) -->
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>$(TestLEModsPath)\..\MelonLoader\Il2CppAssemblies\UnityEngine.CoreModule.dll</HintPath>
      <Private>false</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>$(TestLEModsPath)\..\MelonLoader\Il2CppAssemblies\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>false</Private>
    </Reference>
  </ItemGroup>

  <!-- Copy DLL to ScriptLibraries folder after build -->
  <Target Name="CopyToScriptLibraries" AfterTargets="Build">
    <MakeDir Directories="$(ScriptLibrariesPath)" Condition="!Exists('$(ScriptLibrariesPath)')" />
    <Copy SourceFiles="$(OutputPath)$(AssemblyName).dll" 
          DestinationFolder="$(ScriptLibrariesPath)" 
          SkipUnchangedFiles="false" />
    <Message Text="Copied $(AssemblyName).dll to $(ScriptLibrariesPath)" Importance="high" />
  </Target>

</Project>
